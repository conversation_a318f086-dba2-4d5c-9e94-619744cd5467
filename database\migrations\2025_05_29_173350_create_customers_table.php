<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('phone_number')->nullable(); // For WhatsApp
            $table->string('name');
            $table->string('chat_id')->nullable(); // For Telegram
            $table->enum('account_type', ['whatsapp', 'telegram']);
            $table->json('metadata')->nullable(); // Additional customer data
            $table->timestamps();

            // Indexes
            $table->index('phone_number');
            $table->index('chat_id');
            $table->index('account_type');
            $table->index('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
