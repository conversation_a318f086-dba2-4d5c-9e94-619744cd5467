<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Customer Selection Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            {{ $this->form }}
        </div>

        <!-- Chat Interface -->
        @if($selectedCustomer)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
                <!-- <PERSON><PERSON>er -->
                <div class="bg-blue-600 text-white p-4 flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-semibold">
                            {{ $customers[$selectedCustomer] ?? 'Unknown Customer' }}
                        </h3>
                        <p class="text-blue-100 text-sm">Telegram Chat</p>
                    </div>
                </div>

                <!-- Messages Container -->
                <div class="h-96 overflow-y-auto p-4 space-y-3 bg-gray-50 dark:bg-gray-900" 
                     style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); background-attachment: fixed;">
                    
                    @if(empty($messages))
                        <div class="text-center text-white py-8">
                            <svg class="w-16 h-16 mx-auto mb-4 text-white opacity-50" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                            </svg>
                            <p>No messages yet. Start a conversation!</p>
                        </div>
                    @else
                        @php
                            $currentDate = null;
                        @endphp
                        
                        @foreach($messages as $message)
                            @if($currentDate !== $message['formatted_date'])
                                @php $currentDate = $message['formatted_date']; @endphp
                                <div class="text-center my-4">
                                    <span class="bg-white bg-opacity-20 backdrop-blur-sm px-3 py-1 rounded-full text-xs text-white shadow">
                                        {{ $message['formatted_date'] }}
                                    </span>
                                </div>
                            @endif

                            <div class="flex {{ $message['is_from_customer'] ? 'justify-start' : 'justify-end' }}">
                                <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-2xl {{ $message['is_from_customer'] 
                                    ? 'bg-white text-gray-800 shadow-lg' 
                                    : 'bg-blue-500 text-white shadow-lg' }}">
                                    <p class="text-sm">{{ $message['message'] }}</p>
                                    <p class="text-xs mt-1 {{ $message['is_from_customer'] ? 'text-gray-500' : 'text-blue-100' }}">
                                        {{ $message['formatted_time'] }}
                                        @if(!$message['is_from_customer'])
                                            <svg class="inline w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                            </svg>
                                        @endif
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    @endif
                </div>

                <!-- Message Input -->
                <div class="p-4 bg-white dark:bg-gray-800 border-t">
                    <form wire:submit="sendMessage" class="flex space-x-3">
                        <div class="flex-1">
                            <textarea 
                                wire:model="newMessage" 
                                placeholder="Type a message..."
                                rows="2"
                                class="w-full border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-blue-500 focus:border-blue-500"
                                wire:keydown.ctrl.enter="sendMessage"
                            ></textarea>
                        </div>
                        <button 
                            type="submit"
                            class="bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-lg transition-colors duration-200"
                            :disabled="!$wire.newMessage"
                        >
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                            </svg>
                        </button>
                    </form>
                    <p class="text-xs text-gray-500 mt-2">Press Ctrl+Enter to send</p>
                </div>
            </div>
        @else
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-8 text-center">
                <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                </svg>
                <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">No Customer Selected</h3>
                <p class="text-gray-500">Select a customer from the dropdown above to start chatting.</p>
            </div>
        @endif
    </div>

    <script>
        // Auto-scroll to bottom when new messages arrive
        document.addEventListener('livewire:updated', () => {
            const messagesContainer = document.querySelector('.overflow-y-auto');
            if (messagesContainer) {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        });

        // Auto-refresh messages every 30 seconds
        setInterval(() => {
            @this.call('refreshMessages');
        }, 30000);
    </script>
</x-filament-panels::page>
