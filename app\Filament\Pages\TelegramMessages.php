<?php

namespace App\Filament\Pages;

use App\Models\Customer;
use App\Models\Message;
use App\Models\Account;
use App\Services\BotResponseService;
use Filament\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;

class TelegramMessages extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-oval-left';
    protected static string $view = 'filament.pages.telegram-messages';
    protected static ?string $navigationGroup = 'Messages';
    protected static ?string $navigationLabel = 'Telegram Messages';
    protected static ?int $navigationSort = 2;

    public ?string $selectedCustomer = null;
    public ?string $newMessage = null;
    public array $messages = [];
    public array $customers = [];

    public function mount(): void
    {
        $this->loadCustomers();
        if (!empty($this->customers)) {
            $this->selectedCustomer = array_key_first($this->customers);
            $this->loadMessages();
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedCustomer')
                    ->label('Select Customer')
                    ->options($this->customers)
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedCustomer = $state;
                        $this->loadMessages();
                    }),

                Textarea::make('newMessage')
                    ->label('Type your message...')
                    ->placeholder('Type your message here...')
                    ->rows(3)
                    ->suffixAction(
                        Action::make('send')
                            ->icon('heroicon-o-paper-airplane')
                            ->color('info')
                            ->action('sendMessage')
                    ),
            ]);
    }

    public function loadCustomers(): void
    {
        $customers = Customer::where('account_type', 'telegram')
            ->where(function ($query) {
                $query->whereHas('orders')
                    ->orWhereExists(function ($subQuery) {
                        $subQuery->select('id')
                            ->from('messages')
                            ->whereColumn('messages.sender', 'customers.chat_id')
                            ->where('messages.channel', 'telegram');
                    });
            })
            ->get();

        $this->customers = $customers->mapWithKeys(function ($customer) {
            return [$customer->chat_id => $customer->display_name . ' (' . $customer->chat_id . ')'];
        })->toArray();
    }

    public function loadMessages(): void
    {
        if (!$this->selectedCustomer) {
            $this->messages = [];
            return;
        }

        $messages = Message::where('channel', 'telegram')
            ->where('sender', $this->selectedCustomer)
            ->orderBy('timestamp', 'asc')
            ->get();

        $this->messages = $messages->map(function ($message) {
            return [
                'id' => $message->id,
                'message' => $message->message,
                'timestamp' => $message->timestamp,
                'formatted_time' => $message->formatted_time,
                'formatted_date' => $message->formatted_date,
                'direction' => $message->direction,
                'is_from_customer' => $message->is_from_customer,
            ];
        })->toArray();
    }

    public function sendMessage(): void
    {
        if (!$this->newMessage || !$this->selectedCustomer) {
            return;
        }

        try {
            // Get the customer
            $customer = Customer::where('chat_id', $this->selectedCustomer)
                ->where('account_type', 'telegram')
                ->first();

            if (!$customer) {
                Notification::make()
                    ->title('Error')
                    ->body('Customer not found.')
                    ->danger()
                    ->send();
                return;
            }

            // Get the first active Telegram account for the current user
            $account = Account::where('type', 'telegram')
                ->where('status', true)
                ->where('user_id', Auth::id())
                ->first();

            if (!$account) {
                Notification::make()
                    ->title('Error')
                    ->body('No active Telegram account found.')
                    ->danger()
                    ->send();
                return;
            }

            // Create outgoing message record
            $message = Message::create([
                'account_id' => $account->id,
                'sender' => $this->selectedCustomer,
                'message' => $this->newMessage,
                'timestamp' => now(),
                'channel' => 'telegram',
                'direction' => 'outgoing',
            ]);

            // Send message via BotResponseService
            $botService = app(BotResponseService::class);
            $botService->sendMessage($customer, $this->newMessage, $account);

            // Clear the message input
            $this->newMessage = null;

            // Reload messages
            $this->loadMessages();

            Notification::make()
                ->title('Message Sent')
                ->body('Your message has been sent successfully.')
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Failed to send message: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    #[On('refresh-messages')]
    public function refreshMessages(): void
    {
        $this->loadMessages();
    }

    public function getTitle(): string
    {
        return 'Telegram Messages';
    }
}
