<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Account;
use App\Models\Customer;
use App\Models\Message;
use Carbon\Carbon;

class MessageTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test user if none exists
        $user = User::first();
        if (!$user) {
            $user = User::create([
                'name' => 'Test Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);
        }

        // Create WhatsApp account
        $whatsappAccount = Account::firstOrCreate([
            'user_id' => $user->id,
            'type' => 'whatsapp',
        ], [
            'name' => 'Test WhatsApp Account',
            'credentials' => ['api_key' => 'test_key'],
            'status' => true,
        ]);

        // Create Telegram account
        $telegramAccount = Account::firstOrCreate([
            'user_id' => $user->id,
            'type' => 'telegram',
        ], [
            'name' => 'Test Telegram Account',
            'credentials' => ['bot_token' => 'test_token'],
            'status' => true,
        ]);

        // Create WhatsApp customers
        $whatsappCustomers = [
            [
                'name' => 'John Doe',
                'phone_number' => '+************',
                'account_type' => 'whatsapp',
            ],
            [
                'name' => 'Jane Smith',
                'phone_number' => '+628*********',
                'account_type' => 'whatsapp',
            ],
        ];

        // Create Telegram customers
        $telegramCustomers = [
            [
                'name' => 'Bob Wilson',
                'chat_id' => '*********',
                'account_type' => 'telegram',
            ],
            [
                'name' => 'Alice Brown',
                'chat_id' => '*********',
                'account_type' => 'telegram',
            ],
        ];

        // Insert customers
        foreach ($whatsappCustomers as $customerData) {
            $customer = Customer::firstOrCreate([
                'phone_number' => $customerData['phone_number'],
                'account_type' => 'whatsapp',
            ], $customerData);

            // Create sample messages for WhatsApp
            $this->createSampleMessages($whatsappAccount, $customer, 'whatsapp');
        }

        foreach ($telegramCustomers as $customerData) {
            $customer = Customer::firstOrCreate([
                'chat_id' => $customerData['chat_id'],
                'account_type' => 'telegram',
            ], $customerData);

            // Create sample messages for Telegram
            $this->createSampleMessages($telegramAccount, $customer, 'telegram');
        }
    }

    private function createSampleMessages(Account $account, Customer $customer, string $channel): void
    {
        $sender = $channel === 'whatsapp' ? $customer->phone_number : $customer->chat_id;
        
        $messages = [
            [
                'message' => 'Hi, I would like to know about your products',
                'direction' => 'incoming',
                'timestamp' => Carbon::now()->subHours(2),
            ],
            [
                'message' => 'Hello! Thank you for your interest. We have a variety of products available. Would you like to see our catalog?',
                'direction' => 'outgoing',
                'timestamp' => Carbon::now()->subHours(2)->addMinutes(5),
            ],
            [
                'message' => 'Yes, please show me the catalog',
                'direction' => 'incoming',
                'timestamp' => Carbon::now()->subHours(2)->addMinutes(10),
            ],
            [
                'message' => '🛍️ *Products Available:*

📦 *Product A*
💰 Price: $50.00
📊 Stock: 10 units
📝 High quality product

📦 *Product B*
💰 Price: $75.00
📊 Stock: 5 units
📝 Premium quality

To order, type: *order [product name]*',
                'direction' => 'outgoing',
                'timestamp' => Carbon::now()->subHours(2)->addMinutes(12),
            ],
            [
                'message' => 'I want to order Product A',
                'direction' => 'incoming',
                'timestamp' => Carbon::now()->subHours(1),
            ],
            [
                'message' => '✅ Product found!

📦 *Product A*
💰 Price: $50.00
📊 Stock available: 10 units

To confirm your order, please reply with: *confirm order Product A*',
                'direction' => 'outgoing',
                'timestamp' => Carbon::now()->subHours(1)->addMinutes(2),
            ],
            [
                'message' => 'confirm order Product A',
                'direction' => 'incoming',
                'timestamp' => Carbon::now()->subMinutes(30),
            ],
            [
                'message' => '🎉 Order confirmed! Your order has been placed successfully. You will receive a payment link shortly.',
                'direction' => 'outgoing',
                'timestamp' => Carbon::now()->subMinutes(25),
            ],
        ];

        foreach ($messages as $messageData) {
            Message::create([
                'account_id' => $account->id,
                'sender' => $sender,
                'message' => $messageData['message'],
                'timestamp' => $messageData['timestamp'],
                'channel' => $channel,
                'direction' => $messageData['direction'],
            ]);
        }
    }
}
