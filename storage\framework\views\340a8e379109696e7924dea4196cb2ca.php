<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Customer Selection Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <?php echo e($this->form); ?>

        </div>

        <!-- Chat Interface -->
        <!--[if BLOCK]><![endif]--><?php if($selectedCustomer): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
                <!-- Chat Header -->
                <div class="bg-green-600 text-white p-4 flex items-center space-x-3">
                    <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-semibold">
                            <?php echo e($customers[$selectedCustomer] ?? 'Unknown Customer'); ?>

                        </h3>
                        <p class="text-green-100 text-sm">WhatsApp Chat</p>
                    </div>
                </div>

                <!-- Messages Container -->
                <div class="h-96 overflow-y-auto p-4 space-y-3 bg-gray-50 dark:bg-gray-900" 
                     style="background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"whatsapp-bg\" x=\"0\" y=\"0\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"%23e5e7eb\" opacity=\"0.3\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23whatsapp-bg)\"/></svg>');">
                    
                    <!--[if BLOCK]><![endif]--><?php if(empty($messages)): ?>
                        <div class="text-center text-gray-500 py-8">
                            <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                            </svg>
                            <p>No messages yet. Start a conversation!</p>
                        </div>
                    <?php else: ?>
                        <?php
                            $currentDate = null;
                        ?>
                        
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $messages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <!--[if BLOCK]><![endif]--><?php if($currentDate !== $message['formatted_date']): ?>
                                <?php $currentDate = $message['formatted_date']; ?>
                                <div class="text-center my-4">
                                    <span class="bg-white dark:bg-gray-700 px-3 py-1 rounded-full text-xs text-gray-500 shadow">
                                        <?php echo e($message['formatted_date']); ?>

                                    </span>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <div class="flex <?php echo e($message['is_from_customer'] ? 'justify-start' : 'justify-end'); ?>">
                                <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg <?php echo e($message['is_from_customer'] 
                                    ? 'bg-white text-gray-800 shadow' 
                                    : 'bg-green-500 text-white'); ?>">
                                    <p class="text-sm"><?php echo e($message['message']); ?></p>
                                    <p class="text-xs mt-1 <?php echo e($message['is_from_customer'] ? 'text-gray-500' : 'text-green-100'); ?>">
                                        <?php echo e($message['formatted_time']); ?>

                                        <!--[if BLOCK]><![endif]--><?php if(!$message['is_from_customer']): ?>
                                            <svg class="inline w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                            </svg>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </p>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <!-- Message Input -->
                <div class="p-4 bg-white dark:bg-gray-800 border-t">
                    <form wire:submit="sendMessage" class="flex space-x-3">
                        <div class="flex-1">
                            <textarea 
                                wire:model="newMessage" 
                                placeholder="Type a message..."
                                rows="2"
                                class="w-full border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-green-500 focus:border-green-500"
                                wire:keydown.ctrl.enter="sendMessage"
                            ></textarea>
                        </div>
                        <button 
                            type="submit"
                            class="bg-green-500 hover:bg-green-600 text-white p-3 rounded-lg transition-colors duration-200"
                            :disabled="!$wire.newMessage"
                        >
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                            </svg>
                        </button>
                    </form>
                    <p class="text-xs text-gray-500 mt-2">Press Ctrl+Enter to send</p>
                </div>
            </div>
        <?php else: ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-8 text-center">
                <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                </svg>
                <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">No Customer Selected</h3>
                <p class="text-gray-500">Select a customer from the dropdown above to start chatting.</p>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <script>
        // Auto-scroll to bottom when new messages arrive
        document.addEventListener('livewire:updated', () => {
            const messagesContainer = document.querySelector('.overflow-y-auto');
            if (messagesContainer) {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        });

        // Auto-refresh messages every 30 seconds
        setInterval(() => {
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('refreshMessages');
        }, 30000);
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\omnibis\resources\views/filament/pages/whatsapp-messages.blade.php ENDPATH**/ ?>