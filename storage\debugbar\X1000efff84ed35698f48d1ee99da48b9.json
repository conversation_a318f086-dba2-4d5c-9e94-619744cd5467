{"__meta": {"id": "X1000efff84ed35698f48d1ee99da48b9", "datetime": "2025-05-29 23:57:16", "utime": **********.036568, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.3.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.17938, "end": **********.03659, "duration": 0.8572101593017578, "duration_str": "857ms", "measures": [{"label": "Booting", "start": **********.17938, "relative_start": 0, "end": **********.576717, "relative_end": **********.576717, "duration": 0.3973369598388672, "duration_str": "397ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.576735, "relative_start": 0.3973550796508789, "end": **********.036593, "relative_end": 2.86102294921875e-06, "duration": 0.4598579406738281, "duration_str": "460ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45095616, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.914922, "type": "blade", "hash": "bladeD:\\laragon\\www\\omnibis\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::5e93d402ed1f3da8a07f4840136a03cb", "param_count": null, "params": [], "start": **********.945952, "type": "blade", "hash": "bladeD:\\laragon\\www\\omnibis\\storage\\framework\\views/5e93d402ed1f3da8a07f4840136a03cb.blade.php__components::5e93d402ed1f3da8a07f4840136a03cb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fstorage%2Fframework%2Fviews%2F5e93d402ed1f3da8a07f4840136a03cb.blade.php&line=1", "ajax": false, "filename": "5e93d402ed1f3da8a07f4840136a03cb.blade.php", "line": "?"}}, {"name": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.020177, "type": "blade", "hash": "bladeD:\\laragon\\www\\omnibis\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}}, {"name": "filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.020617, "type": "blade", "hash": "bladeD:\\laragon\\www\\omnibis\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}}, {"name": "__components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.027667, "type": "blade", "hash": "bladeD:\\laragon\\www\\omnibis\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "domain": null, "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "uses": "Filament\\Pages\\Dashboard@__invoke", "controller": "Filament\\Pages\\Dashboard@render", "as": "filament.admin.pages.dashboard", "namespace": null, "prefix": "/", "where": [], "excluded_middleware": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>"}, "queries": {"nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.009660000000000002, "accumulated_duration_str": "9.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks' limit 1", "type": "query", "params": [], "bindings": ["ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.60255, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "omnibis", "explain": null, "start_percent": 0, "width_percent": 9.42}, {"sql": "select * from `users` where `id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.845796, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omnibis", "explain": null, "start_percent": 9.42, "width_percent": 6.832}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (11) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8494651, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omnibis", "explain": null, "start_percent": 16.253, "width_percent": 7.039}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.8534641, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "omnibis", "explain": null, "start_percent": 23.292, "width_percent": 4.969}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:11', 'illuminate:cache:flexible:created:filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11", "illuminate:cache:flexible:created:filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 362}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 534}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.854991, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:387", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=387", "ajax": false, "filename": "DatabaseStore.php", "line": "387"}, "connection": "omnibis", "explain": null, "start_percent": 28.261, "width_percent": 6.315}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.858734, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "omnibis", "explain": null, "start_percent": 34.576, "width_percent": 7.246}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.8606021, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "omnibis", "explain": null, "start_percent": 41.822, "width_percent": 6.936}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.862113, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "omnibis", "explain": null, "start_percent": 48.758, "width_percent": 6.108}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.863577, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "omnibis", "explain": null, "start_percent": 54.865, "width_percent": 6.108}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 246}], "start": **********.86507, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "omnibis", "explain": null, "start_percent": 60.973, "width_percent": 8.282}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.917566, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "omnibis", "explain": null, "start_percent": 69.255, "width_percent": 7.453}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiYWs1V1JCT2tyUnpvT2d2S3h3c2FBdUtyQ1FNc280TmNneU5LaTZZTCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjE5OiJodHRwOi8vb21uaWJpcy50ZXN0Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiR5VDBFMFZ2S0xjWlBpZnhsb0JES0JlM0RxdnB6YTlXdFM1MlQ2NWlGNGQxU2d3OTVVSW1MNiI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==', `last_activity` = **********, `user_id` = 11, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiYWs1V1JCT2tyUnpvT2d2S3h3c2FBdUtyQ1FNc280TmNneU5LaTZZTCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjE5OiJodHRwOi8vb21uaWJpcy50ZXN0Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiR5VDBFMFZ2S0xjWlBpZnhsb0JES0JlM0RxdnB6YTlXdFM1MlQ2NWlGNGQxU2d3OTVVSW1MNiI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==", **********, 11, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 176}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.0320241, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "omnibis", "explain": null, "start_percent": 76.708, "width_percent": 23.292}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"filament.pages.dashboard #FrqSFMfNVDoqbDYr3PjI": "array:4 [\n  \"data\" => array:14 [\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"filament.pages.dashboard\"\n  \"component\" => \"Filament\\Pages\\Dashboard\"\n  \"id\" => \"FrqSFMfNVDoqbDYr3PjI\"\n]", "filament.widgets.account-widget #BBFGRCvM0htLsPti6eTJ": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament.widgets.account-widget\"\n  \"component\" => \"Filament\\Widgets\\AccountWidget\"\n  \"id\" => \"BBFGRCvM0htLsPti6eTJ\"\n]", "filament.widgets.filament-info-widget #ER1CGYRl30NqYlOIREwm": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament.widgets.filament-info-widget\"\n  \"component\" => \"Filament\\Widgets\\FilamentInfoWidget\"\n  \"id\" => \"ER1CGYRl30NqYlOIREwm\"\n]", "filament.livewire.notifications #ZARafEnzfbvK6JLYV2BI": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2305\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"ZARafEnzfbvK6JLYV2BI\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 14, "messages": [{"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => null,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-192604780 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-192604780\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.921874, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => null,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-829324315 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-829324315\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.932521, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => false,\n  user => 11,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1630351071 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630351071\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.932708, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => null,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-691671928 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-691671928\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.934659, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => false,\n  user => 11,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1159812551 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1159812551\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.934851, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => null,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-33218580 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-33218580\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.936531, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => false,\n  user => 11,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-626536251 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-626536251\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.936725, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => null,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-848240201 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-848240201\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.966011, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => null,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-575801637 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575801637\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.969555, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => false,\n  user => 11,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1107479171 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1107479171\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.969749, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => null,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-385506308 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-385506308\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.970494, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => false,\n  user => 11,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1615891039 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615891039\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.970686, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => null,\n  user => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1549438090 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1549438090\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.971382, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => false,\n  user => 11,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2043723452 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2043723452\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.971577, "xdebug_link": null}]}, "session": {"_token": "ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://omnibis.test\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "11", "password_hash_web": "$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6", "filament": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1000729772 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1000729772\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1667517043 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1667517043\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2067053643 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2067053643\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-133158095 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1175 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IklUM2tlTG5UL1FqZHdYQUpFQlFCK1E9PSIsInZhbHVlIjoiNDFRVEFBanE2Y3l2WVVEU2hGUC9KTHNMWXQ1WGVkemRUck9IRDRSSHk4ZzVieVhnQWVLMzNld1p3UEdpU0lvQU0vc0Jvc3d4TjZIUEV3MVN3Q3hON1ljUm5yVTVzbGQ1V0NFcHcxdWg3TkR4cHpzOFhhbXE2eDhOaGhXOXRXT1pBWXpiSGN2bUY0b3lyeUhvM29Mam5QNUNERlRjY0Q0TXFEQWo5ajAxU2JBPSIsIm1hYyI6ImVhNjYwYjg3YzFhMTIzODg2M2ZkMjhlOTkwOTMwYWRlZDZjZDYyNDU4MTY5MGYzNzc3MzlmNGVkMmU3ODMwODUiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6ImQ5Z29WZDNkWmdSeEZmUHNpMi92aVE9PSIsInZhbHVlIjoiUUxUZEQ5Z2JNRFo0VFcyZGNRN2M4MmlDVHU1U0RoWTg5VkNUbkxGWGw1bHdrYjFKTmpVRWZVSkdOR0RGRHQrd0dlRFQ5VG1aQXdtWk9NMFlsRjIwQzBzcEk1MTV4S1RUSUxWWlRCR05Ndnh4dHFSSnc2Ymx3QjExamkxSjVjSGEiLCJtYWMiOiI2Zjg1NDA1MDRhNzIzOWEyZjNhNzU5ZDRkODAxMmFhZDE4MGEwMTVhOWIzZTIxMjM0M2E0Mjc2MzMwMjZlZjI1IiwidGFnIjoiIn0%3D; zapinflow_session=eyJpdiI6Iks5ZzI0OW5qWmRwOFk1L0hROHJmOGc9PSIsInZhbHVlIjoiZWthcnMxS2dINFRrVWJVWk8vTG1DU1pKZkUvLzQweDhBdE03dkh3dnNVeDN3MG5XRmVzc0ZVcVlBaFloUU1VWUJHWndHYXpJbDFEMW43RGNzbHhvNHZ6K2I4eDRpaTlGV0FGdHlUb0k5RUsyWWg0UzlrTTdXL2k5NjJyZHJOK3QiLCJtYWMiOiIzNzQwZGExMmFmMjJlMGEzNDRjODQ4YjkzZTFmMGY5ZDllNGMwZDk1M2FmNjg1NzE4ZTRiOTA2ZWQ4NjRmZDNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,id;q=0.8,ms;q=0.7,th;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://omnibis.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">omnibis.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133158095\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1508478508 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"74 characters\">11|o5UxToU2uu|$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>zapinflow_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508478508\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-522053057 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 29 May 2025 23:57:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlhwOE5QbWd3Y0pDNlYxaWI2aU0xM0E9PSIsInZhbHVlIjoiZzFQcWFFdTh5cHI2TlNRNHJwUUQ3UVhTZnVTQnIwdUVzd2NCYmNtTXJvNUFTUzY2UzNvVmg0eTJ0elBMa2xsVWczMDFFb0RDSy85QWNsUTZhc3NFYzhFMWxHS05NbXJIK1pqcGpJTFJtWmhhcHVJYm5TV0lpVWJ3QWVTbGtWSEoiLCJtYWMiOiJkMzQ1MGFiZWU3YWZiMGVlZWIyYTk2MzhhYWE1ZTdiMzc4N2JmYmIzMGU1NjljNmI0ODg1MzRhZGI0ZWZiNGY5IiwidGFnIjoiIn0%3D; expires=Fri, 30 May 2025 01:57:16 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">zapinflow_session=eyJpdiI6IkcxMnI2eW9peTJKK1N3Y2pmaVowVkE9PSIsInZhbHVlIjoibXhGSUdnYWsxZWFqRGNtVVVRdENVSHFTM1lpcWd3OUlUYnpmSVpvOTBMRXZka3dlc1A2ZGMwdUFJZWtvWk51WW5MbytTcitZWWlvckJXRzczLzEzVXlNRW1TS0dBbXJjN0EyN3dVUTQvV0JsQTRPcU1hLzQwM3U3WVFGZERocUoiLCJtYWMiOiIyMzc3ODZlOGJlM2MyMTRlNDE2ODQxOTY1ZGI3NDQyZGMxMjhmOTVhYjg0YjEzZTI5MThhMjZhOGRiMTQ1NmE0IiwidGFnIjoiIn0%3D; expires=Fri, 30 May 2025 01:57:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlhwOE5QbWd3Y0pDNlYxaWI2aU0xM0E9PSIsInZhbHVlIjoiZzFQcWFFdTh5cHI2TlNRNHJwUUQ3UVhTZnVTQnIwdUVzd2NCYmNtTXJvNUFTUzY2UzNvVmg0eTJ0elBMa2xsVWczMDFFb0RDSy85QWNsUTZhc3NFYzhFMWxHS05NbXJIK1pqcGpJTFJtWmhhcHVJYm5TV0lpVWJ3QWVTbGtWSEoiLCJtYWMiOiJkMzQ1MGFiZWU3YWZiMGVlZWIyYTk2MzhhYWE1ZTdiMzc4N2JmYmIzMGU1NjljNmI0ODg1MzRhZGI0ZWZiNGY5IiwidGFnIjoiIn0%3D; expires=Fri, 30-May-2025 01:57:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">zapinflow_session=eyJpdiI6IkcxMnI2eW9peTJKK1N3Y2pmaVowVkE9PSIsInZhbHVlIjoibXhGSUdnYWsxZWFqRGNtVVVRdENVSHFTM1lpcWd3OUlUYnpmSVpvOTBMRXZka3dlc1A2ZGMwdUFJZWtvWk51WW5MbytTcitZWWlvckJXRzczLzEzVXlNRW1TS0dBbXJjN0EyN3dVUTQvV0JsQTRPcU1hLzQwM3U3WVFGZERocUoiLCJtYWMiOiIyMzc3ODZlOGJlM2MyMTRlNDE2ODQxOTY1ZGI3NDQyZGMxMjhmOTVhYjg0YjEzZTI5MThhMjZhOGRiMTQ1NmE0IiwidGFnIjoiIn0%3D; expires=Fri, 30-May-2025 01:57:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-522053057\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1922627555 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"19 characters\">http://omnibis.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1922627555\", {\"maxDepth\":0})</script>\n"}}