<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Account;
use App\Models\Customer;
use App\Models\Message;
use App\Models\Order;
use App\Models\Product;
use App\Services\BotResponseService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class WebhookController extends Controller
{
    protected BotResponseService $botResponseService;

    public function __construct(BotResponseService $botResponseService)
    {
        $this->botResponseService = $botResponseService;
    }

    /**
     * Handle incoming WhatsApp webhook from n8n
     */
    public function whatsappWebhook(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'account_id' => 'required|exists:accounts,id',
                'sender' => 'required|string',
                'message' => 'required|string',
                'message_id' => 'nullable|string',
                'metadata' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 400);
            }

            $data = $validator->validated();
            
            // Find or create customer
            $customer = $this->findOrCreateCustomer($data['sender'], 'whatsapp');
            
            // Store incoming message
            $message = Message::create([
                'account_id' => $data['account_id'],
                'sender' => $data['sender'],
                'message' => $data['message'],
                'timestamp' => now(),
                'channel' => 'whatsapp',
                'direction' => 'incoming',
                'message_id' => $data['message_id'] ?? null,
                'metadata' => $data['metadata'] ?? null,
            ]);

            // Process bot response
            $response = $this->botResponseService->processMessage($message, $customer);

            Log::info('WhatsApp webhook processed', [
                'message_id' => $message->id,
                'customer_id' => $customer->id,
                'response' => $response
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Webhook processed successfully',
                'data' => [
                    'message_id' => $message->id,
                    'customer_id' => $customer->id,
                    'bot_response' => $response
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('WhatsApp webhook error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Handle incoming Telegram webhook from n8n
     */
    public function telegramWebhook(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'account_id' => 'required|exists:accounts,id',
                'sender' => 'required|string',
                'message' => 'required|string',
                'message_id' => 'nullable|string',
                'metadata' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 400);
            }

            $data = $validator->validated();
            
            // Find or create customer
            $customer = $this->findOrCreateCustomer($data['sender'], 'telegram');
            
            // Store incoming message
            $message = Message::create([
                'account_id' => $data['account_id'],
                'sender' => $data['sender'],
                'message' => $data['message'],
                'timestamp' => now(),
                'channel' => 'telegram',
                'direction' => 'incoming',
                'message_id' => $data['message_id'] ?? null,
                'metadata' => $data['metadata'] ?? null,
            ]);

            // Process bot response
            $response = $this->botResponseService->processMessage($message, $customer);

            Log::info('Telegram webhook processed', [
                'message_id' => $message->id,
                'customer_id' => $customer->id,
                'response' => $response
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Webhook processed successfully',
                'data' => [
                    'message_id' => $message->id,
                    'customer_id' => $customer->id,
                    'bot_response' => $response
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Telegram webhook error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Handle order status updates from n8n
     */
    public function orderStatusWebhook(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|exists:orders,id',
                'status' => 'required|in:pending,shipped,delivered',
                'metadata' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 400);
            }

            $data = $validator->validated();
            
            $order = Order::findOrFail($data['order_id']);
            $order->update([
                'status' => $data['status'],
                'custom_fields' => array_merge($order->custom_fields ?? [], $data['metadata'] ?? [])
            ]);

            // Send notification to customer
            $this->botResponseService->sendOrderStatusUpdate($order);

            Log::info('Order status updated via webhook', [
                'order_id' => $order->id,
                'new_status' => $data['status']
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Order status updated successfully',
                'data' => [
                    'order_id' => $order->id,
                    'status' => $order->status
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Order status webhook error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Find or create customer based on sender and account type
     */
    private function findOrCreateCustomer(string $sender, string $accountType): Customer
    {
        $query = Customer::where('account_type', $accountType);
        
        if ($accountType === 'whatsapp') {
            $query->where('phone_number', $sender);
        } else {
            $query->where('chat_id', $sender);
        }
        
        $customer = $query->first();
        
        if (!$customer) {
            $customerData = [
                'account_type' => $accountType,
                'name' => $sender, // Default name, can be updated later
            ];
            
            if ($accountType === 'whatsapp') {
                $customerData['phone_number'] = $sender;
            } else {
                $customerData['chat_id'] = $sender;
            }
            
            $customer = Customer::create($customerData);
        }
        
        return $customer;
    }
}
