{"__meta": {"id": "X28508aabd59595f9fcc2c5b7928ae047", "datetime": "2025-05-30 00:20:34", "utime": **********.930788, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.3.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.273948, "end": **********.930826, "duration": 0.6568779945373535, "duration_str": "657ms", "measures": [{"label": "Booting", "start": **********.273948, "relative_start": 0, "end": **********.604677, "relative_end": **********.604677, "duration": 0.33072900772094727, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.604699, "relative_start": 0.3307509422302246, "end": **********.93083, "relative_end": 4.0531158447265625e-06, "duration": 0.32613110542297363, "duration_str": "326ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44443448, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "filament.pages.telegram-messages", "param_count": null, "params": [], "start": **********.885287, "type": "blade", "hash": "bladeD:\\laragon\\www\\omnibis\\resources\\views/filament/pages/telegram-messages.blade.phpfilament.pages.telegram-messages", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fresources%2Fviews%2Ffilament%2Fpages%2Ftelegram-messages.blade.php&line=1", "ajax": false, "filename": "telegram-messages.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.910213, "type": "blade", "hash": "bladeD:\\laragon\\www\\omnibis\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}]}, "route": {"uri": "POST livewire/update", "controller": "App\\Filament\\Pages\\TelegramMessages@refreshMessages", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fapp%2FFilament%2FPages%2FTelegramMessages.php&line=186\" onclick=\"\">app/Filament/Pages/TelegramMessages.php:186-189</a>"}, "queries": {"nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00749, "accumulated_duration_str": "7.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks' limit 1", "type": "query", "params": [], "bindings": ["ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.62141, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "omnibis", "explain": null, "start_percent": 0, "width_percent": 12.283}, {"sql": "select * from `users` where `id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.846781, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omnibis", "explain": null, "start_percent": 12.283, "width_percent": 10.013}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (11) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.85042, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omnibis", "explain": null, "start_percent": 22.296, "width_percent": 9.613}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.854351, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "omnibis", "explain": null, "start_percent": 31.909, "width_percent": 9.212}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:11', 'illuminate:cache:flexible:created:filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11", "illuminate:cache:flexible:created:filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 362}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 534}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.85672, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:387", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=387", "ajax": false, "filename": "DatabaseStore.php", "line": "387"}, "connection": "omnibis", "explain": null, "start_percent": 41.121, "width_percent": 12.684}, {"sql": "select * from `messages` where `channel` = 'telegram' and `sender` = '123456789' order by `timestamp` asc", "type": "query", "params": [], "bindings": ["telegram", "123456789"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/TelegramMessages.php", "file": "D:\\laragon\\www\\omnibis\\app\\Filament\\Pages\\TelegramMessages.php", "line": 100}, {"index": 16, "namespace": null, "name": "app/Filament/Pages/TelegramMessages.php", "file": "D:\\laragon\\www\\omnibis\\app\\Filament\\Pages\\TelegramMessages.php", "line": 188}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.871168, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "TelegramMessages.php:100", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/TelegramMessages.php", "file": "D:\\laragon\\www\\omnibis\\app\\Filament\\Pages\\TelegramMessages.php", "line": 100}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fapp%2FFilament%2FPages%2FTelegramMessages.php&line=100", "ajax": false, "filename": "TelegramMessages.php", "line": "100"}, "connection": "omnibis", "explain": null, "start_percent": 53.805, "width_percent": 14.82}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiYWs1V1JCT2tyUnpvT2d2S3h3c2FBdUtyQ1FNc280TmNneU5LaTZZTCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM3OiJodHRwOi8vb21uaWJpcy50ZXN0L3RlbGVncmFtLW1lc3NhZ2VzIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiR5VDBFMFZ2S0xjWlBpZnhsb0JES0JlM0RxdnB6YTlXdFM1MlQ2NWlGNGQxU2d3OTVVSW1MNiI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==', `last_activity` = **********, `user_id` = 11, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiYWs1V1JCT2tyUnpvT2d2S3h3c2FBdUtyQ1FNc280TmNneU5LaTZZTCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM3OiJodHRwOi8vb21uaWJpcy50ZXN0L3RlbGVncmFtLW1lc3NhZ2VzIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiR5VDBFMFZ2S0xjWlBpZnhsb0JES0JlM0RxdnB6YTlXdFM1MlQ2NWlGNGQxU2d3OTVVSW1MNiI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==", **********, 11, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 176}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.9259448, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "omnibis", "explain": null, "start_percent": 68.625, "width_percent": 31.375}]}, "models": {"data": {"App\\Models\\Message": {"value": 16, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fapp%2FModels%2FMessage.php&line=1", "ajax": false, "filename": "Message.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 17, "is_counter": true}, "livewire": {"data": {"app.filament.pages.telegram-messages #kp4KnPN5RtuBqCFr1jiF": "array:4 [\n  \"data\" => array:18 [\n    \"selectedCustomer\" => \"123456789\"\n    \"newMessage\" => null\n    \"messages\" => array:16 [\n      0 => array:7 [\n        \"id\" => 17\n        \"message\" => \"Hi, I would like to know about your products\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748555958 {#3568\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000df00000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 21:59:18.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"21:59\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"incoming\"\n        \"is_from_customer\" => true\n      ]\n      1 => array:7 [\n        \"id\" => 18\n        \"message\" => \"Hello! Thank you for your interest. We have a variety of products available. Would you like to see our catalog?\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748556258 {#3565\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000ded0000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 22:04:18.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"22:04\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"outgoing\"\n        \"is_from_customer\" => false\n      ]\n      2 => array:7 [\n        \"id\" => 19\n        \"message\" => \"Yes, please show me the catalog\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748556558 {#3562\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000dea0000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 22:09:18.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"22:09\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"incoming\"\n        \"is_from_customer\" => true\n      ]\n      3 => array:7 [\n        \"id\" => 20\n        \"message\" => \"\"\"\n          🛍️ *Products Available:*\\n\n          \\n\n          📦 *Product A*\\n\n          💰 Price: $50.00\\n\n          📊 Stock: 10 units\\n\n          📝 High quality product\\n\n          \\n\n          📦 *Product B*\\n\n          💰 Price: $75.00\\n\n          📊 Stock: 5 units\\n\n          📝 Premium quality\\n\n          \\n\n          To order, type: *order [product name]*\n          \"\"\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748556678 {#3561\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000de90000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 22:11:18.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"22:11\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"outgoing\"\n        \"is_from_customer\" => false\n      ]\n      4 => array:7 [\n        \"id\" => 49\n        \"message\" => \"Hi, I would like to know about your products\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748557069 {#3560\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000de80000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 22:17:49.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"22:17\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"incoming\"\n        \"is_from_customer\" => true\n      ]\n      5 => array:7 [\n        \"id\" => 50\n        \"message\" => \"Hello! Thank you for your interest. We have a variety of products available. Would you like to see our catalog?\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748557369 {#3559\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000de70000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 22:22:49.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"22:22\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"outgoing\"\n        \"is_from_customer\" => false\n      ]\n      6 => array:7 [\n        \"id\" => 51\n        \"message\" => \"Yes, please show me the catalog\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748557669 {#3557\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000de50000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 22:27:49.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"22:27\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"incoming\"\n        \"is_from_customer\" => true\n      ]\n      7 => array:7 [\n        \"id\" => 52\n        \"message\" => \"\"\"\n          🛍️ *Products Available:*\\n\n          \\n\n          📦 *Product A*\\n\n          💰 Price: $50.00\\n\n          📊 Stock: 10 units\\n\n          📝 High quality product\\n\n          \\n\n          📦 *Product B*\\n\n          💰 Price: $75.00\\n\n          📊 Stock: 5 units\\n\n          📝 Premium quality\\n\n          \\n\n          To order, type: *order [product name]*\n          \"\"\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748557789 {#3556\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000de40000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 22:29:49.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"22:29\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"outgoing\"\n        \"is_from_customer\" => false\n      ]\n      8 => array:7 [\n        \"id\" => 21\n        \"message\" => \"I want to order Product A\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748559558 {#3555\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000de30000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 22:59:18.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"22:59\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"incoming\"\n        \"is_from_customer\" => true\n      ]\n      9 => array:7 [\n        \"id\" => 22\n        \"message\" => \"\"\"\n          ✅ Product found!\\n\n          \\n\n          📦 *Product A*\\n\n          💰 Price: $50.00\\n\n          📊 Stock available: 10 units\\n\n          \\n\n          To confirm your order, please reply with: *confirm order Product A*\n          \"\"\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748559678 {#3553\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000de10000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 23:01:18.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"23:01\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"outgoing\"\n        \"is_from_customer\" => false\n      ]\n      10 => array:7 [\n        \"id\" => 53\n        \"message\" => \"I want to order Product A\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748560669 {#3552\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000de00000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 23:17:49.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"23:17\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"incoming\"\n        \"is_from_customer\" => true\n      ]\n      11 => array:7 [\n        \"id\" => 54\n        \"message\" => \"\"\"\n          ✅ Product found!\\n\n          \\n\n          📦 *Product A*\\n\n          💰 Price: $50.00\\n\n          📊 Stock available: 10 units\\n\n          \\n\n          To confirm your order, please reply with: *confirm order Product A*\n          \"\"\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748560789 {#3550\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000dde0000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 23:19:49.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"23:19\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"outgoing\"\n        \"is_from_customer\" => false\n      ]\n      12 => array:7 [\n        \"id\" => 23\n        \"message\" => \"confirm order Product A\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748561358 {#3547\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000ddb0000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 23:29:18.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"23:29\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"incoming\"\n        \"is_from_customer\" => true\n      ]\n      13 => array:7 [\n        \"id\" => 24\n        \"message\" => \"🎉 Order confirmed! Your order has been placed successfully. You will receive a payment link shortly.\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748561658 {#3549\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000ddd0000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 23:34:18.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"23:34\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"outgoing\"\n        \"is_from_customer\" => false\n      ]\n      14 => array:7 [\n        \"id\" => 55\n        \"message\" => \"confirm order Product A\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748562469 {#3542\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000dd60000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 23:47:49.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"23:47\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"incoming\"\n        \"is_from_customer\" => true\n      ]\n      15 => array:7 [\n        \"id\" => 56\n        \"message\" => \"🎉 Order confirmed! Your order has been placed successfully. You will receive a payment link shortly.\"\n        \"timestamp\" => Illuminate\\Support\\Carbon @1748562769 {#3546\n          #endOfTime: false\n          #startOfTime: false\n          #constructedObjectId: \"0000000000000dda0000000000000000\"\n          -clock: null\n          #localMonthsOverflow: null\n          #localYearsOverflow: null\n          #localStrictModeEnabled: null\n          #localHumanDiffOptions: null\n          #localToStringFormat: null\n          #localSerializer: null\n          #localMacros: null\n          #localGenericMacros: null\n          #localFormatFunction: null\n          #localTranslator: null\n          #dumpProperties: array:3 [\n            0 => \"date\"\n            1 => \"timezone_type\"\n            2 => \"timezone\"\n          ]\n          #dumpLocale: null\n          #dumpDateProperties: null\n          date: 2025-05-29 23:52:49.0 UTC (+00:00)\n        }\n        \"formatted_time\" => \"23:52\"\n        \"formatted_date\" => \"29/05/2025\"\n        \"direction\" => \"outgoing\"\n        \"is_from_customer\" => false\n      ]\n    ]\n    \"customers\" => array:2 [\n      123456789 => \"Bob Wilson (123456789)\"\n      987654321 => \"Alice Brown (987654321)\"\n    ]\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.pages.telegram-messages\"\n  \"component\" => \"App\\Filament\\Pages\\TelegramMessages\"\n  \"id\" => \"kp4KnPN5RtuBqCFr1jiF\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://omnibis.test/telegram-messages\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "11", "password_hash_web": "$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6", "filament": "[]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-1084984243 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1084984243\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-432856133 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-432856133\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1142872989 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"6193 characters\">{&quot;data&quot;:{&quot;selectedCustomer&quot;:&quot;123456789&quot;,&quot;newMessage&quot;:null,&quot;messages&quot;:[[[{&quot;id&quot;:17,&quot;message&quot;:&quot;Hi, I would like to know about your products&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T21:59:18+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;21:59&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;incoming&quot;,&quot;is_from_customer&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:18,&quot;message&quot;:&quot;Hello! Thank you for your interest. We have a variety of products available. Would you like to see our catalog?&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T22:04:18+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;22:04&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;outgoing&quot;,&quot;is_from_customer&quot;:false},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:19,&quot;message&quot;:&quot;Yes, please show me the catalog&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T22:09:18+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;22:09&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;incoming&quot;,&quot;is_from_customer&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:20,&quot;message&quot;:&quot;\\ud83d\\udecd\\ufe0f *Products Available:*\\n\\n\\ud83d\\udce6 *Product A*\\n\\ud83d\\udcb0 Price: $50.00\\n\\ud83d\\udcca Stock: 10 units\\n\\ud83d\\udcdd High quality product\\n\\n\\ud83d\\udce6 *Product B*\\n\\ud83d\\udcb0 Price: $75.00\\n\\ud83d\\udcca Stock: 5 units\\n\\ud83d\\udcdd Premium quality\\n\\nTo order, type: *order [product name]*&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T22:11:18+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;22:11&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;outgoing&quot;,&quot;is_from_customer&quot;:false},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:49,&quot;message&quot;:&quot;Hi, I would like to know about your products&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T22:17:49+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;22:17&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;incoming&quot;,&quot;is_from_customer&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:50,&quot;message&quot;:&quot;Hello! Thank you for your interest. We have a variety of products available. Would you like to see our catalog?&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T22:22:49+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;22:22&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;outgoing&quot;,&quot;is_from_customer&quot;:false},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:51,&quot;message&quot;:&quot;Yes, please show me the catalog&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T22:27:49+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;22:27&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;incoming&quot;,&quot;is_from_customer&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:52,&quot;message&quot;:&quot;\\ud83d\\udecd\\ufe0f *Products Available:*\\n\\n\\ud83d\\udce6 *Product A*\\n\\ud83d\\udcb0 Price: $50.00\\n\\ud83d\\udcca Stock: 10 units\\n\\ud83d\\udcdd High quality product\\n\\n\\ud83d\\udce6 *Product B*\\n\\ud83d\\udcb0 Price: $75.00\\n\\ud83d\\udcca Stock: 5 units\\n\\ud83d\\udcdd Premium quality\\n\\nTo order, type: *order [product name]*&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T22:29:49+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;22:29&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;outgoing&quot;,&quot;is_from_customer&quot;:false},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:21,&quot;message&quot;:&quot;I want to order Product A&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T22:59:18+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;22:59&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;incoming&quot;,&quot;is_from_customer&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:22,&quot;message&quot;:&quot;\\u2705 Product found!\\n\\n\\ud83d\\udce6 *Product A*\\n\\ud83d\\udcb0 Price: $50.00\\n\\ud83d\\udcca Stock available: 10 units\\n\\nTo confirm your order, please reply with: *confirm order Product A*&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T23:01:18+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;23:01&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;outgoing&quot;,&quot;is_from_customer&quot;:false},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:53,&quot;message&quot;:&quot;I want to order Product A&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T23:17:49+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;23:17&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;incoming&quot;,&quot;is_from_customer&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:54,&quot;message&quot;:&quot;\\u2705 Product found!\\n\\n\\ud83d\\udce6 *Product A*\\n\\ud83d\\udcb0 Price: $50.00\\n\\ud83d\\udcca Stock available: 10 units\\n\\nTo confirm your order, please reply with: *confirm order Product A*&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T23:19:49+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;23:19&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;outgoing&quot;,&quot;is_from_customer&quot;:false},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:23,&quot;message&quot;:&quot;confirm order Product A&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T23:29:18+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;23:29&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;incoming&quot;,&quot;is_from_customer&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:24,&quot;message&quot;:&quot;\\ud83c\\udf89 Order confirmed! Your order has been placed successfully. You will receive a payment link shortly.&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T23:34:18+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;23:34&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;outgoing&quot;,&quot;is_from_customer&quot;:false},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:55,&quot;message&quot;:&quot;confirm order Product A&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T23:47:49+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;23:47&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;incoming&quot;,&quot;is_from_customer&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:56,&quot;message&quot;:&quot;\\ud83c\\udf89 Order confirmed! Your order has been placed successfully. You will receive a payment link shortly.&quot;,&quot;timestamp&quot;:[&quot;2025-05-29T23:52:49+00:00&quot;,{&quot;type&quot;:&quot;illuminate&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;formatted_time&quot;:&quot;23:52&quot;,&quot;formatted_date&quot;:&quot;29\\/05\\/2025&quot;,&quot;direction&quot;:&quot;outgoing&quot;,&quot;is_from_customer&quot;:false},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;customers&quot;:[{&quot;123456789&quot;:&quot;Bob Wilson (123456789)&quot;,&quot;987654321&quot;:&quot;Alice Brown (987654321)&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;kp4KnPN5RtuBqCFr1jiF&quot;,&quot;name&quot;:&quot;app.filament.pages.telegram-messages&quot;,&quot;path&quot;:&quot;telegram-messages&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;2cb3ea3ce5d267b966bde1f9f3d119130d908a7b5508a3cf8cdecbc5ae6c1214&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">refreshMessages</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1142872989\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-410640801 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1175 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IklUM2tlTG5UL1FqZHdYQUpFQlFCK1E9PSIsInZhbHVlIjoiNDFRVEFBanE2Y3l2WVVEU2hGUC9KTHNMWXQ1WGVkemRUck9IRDRSSHk4ZzVieVhnQWVLMzNld1p3UEdpU0lvQU0vc0Jvc3d4TjZIUEV3MVN3Q3hON1ljUm5yVTVzbGQ1V0NFcHcxdWg3TkR4cHpzOFhhbXE2eDhOaGhXOXRXT1pBWXpiSGN2bUY0b3lyeUhvM29Mam5QNUNERlRjY0Q0TXFEQWo5ajAxU2JBPSIsIm1hYyI6ImVhNjYwYjg3YzFhMTIzODg2M2ZkMjhlOTkwOTMwYWRlZDZjZDYyNDU4MTY5MGYzNzc3MzlmNGVkMmU3ODMwODUiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6InB4ZW0xMWFWOXFnZDJpbktKWHdPcFE9PSIsInZhbHVlIjoiOUNTaWpTT1F3VEZaWW5sNzYzQmFuaVh3L0ZoQ0JSY0pEWVoyajYvSFcxZXltd1pTT21aODVOWGZpSHA0bm0xQkVpMytPOHBYVlo4aHl1QWFWSysrV0QzTHVoSjNmRFowcTB3MEtiWWpDMjhyblBsT29MNlhRZ3BUcjBNUUhlb0YiLCJtYWMiOiJhOGI3MmNhNmM4N2ZjMTQ5MmI1OTE0YjU1Zjg1ZmI4NTgzZjM2Y2M0NzEwMmMwNzBhOGJhYmE2MzQ3Y2Y0ZmJkIiwidGFnIjoiIn0%3D; zapinflow_session=eyJpdiI6IlVRTFlpWlZqeE1RL09OdFh5MXNKenc9PSIsInZhbHVlIjoidk1uTmRJbHg3UlN4M25XUlVrN055K2kxVXpoTVVCUGdvQTZ2TUZsUUR1c0xJbko4TUs4V0xsMXpCVWhuR1JXbzJiRlZNMURoZndsQVV5R0h6dmJYdlUrM0pZN0loSWI1L0NBRU9UblAvMnMrNW5mT2tSbXhBTkNMUUdwR3FDT2EiLCJtYWMiOiJjMTgyODU3OTlmM2RhYjU3MjFjMzA5OWY5NGI1NmI1NGFjYmIxMDlkMDQ3MmI4YWE4NmQxNDE1NGY1OTczN2E5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,id;q=0.8,ms;q=0.7,th;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://omnibis.test/telegram-messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://omnibis.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">7181</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">omnibis.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-410640801\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-400086558 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"74 characters\">11|o5UxToU2uu|$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>zapinflow_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-400086558\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1437302272 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 30 May 2025 00:20:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkU3VU1uV1dJRWw5VUV4eTEvVkZYYkE9PSIsInZhbHVlIjoiVENMZ1U3Nnk0VDlPU2lzRklSYkJyRTc3SmNTWEJsL25ia2F1WkRhTUd4OVJkOVUySklrM3hxY21OYjdnR1Z5d01LNTNlYjZqRFRFRUxsKytwbC9yWXBBZHhZYmEzbGNjMWZhTkh5eUMrbG1QQUsrQkwrVnVnY1YzNFY4eGd2b3IiLCJtYWMiOiJiYjg2ZmUyOTIxZjY0Yjk4NjhhOWY3OTcxYWU5NDI5YWQxYjc1ZDQ5NDQ5ZDhmZTQ3OGIyODMwN2I4ZGRhNjI0IiwidGFnIjoiIn0%3D; expires=Fri, 30 May 2025 02:20:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">zapinflow_session=eyJpdiI6IlgyRHJFQW5GNTlGamF6TDlYNzhYRlE9PSIsInZhbHVlIjoiUVBmRHBrQWp1RTZWbVhOY3ppakJ0WTZrRmhGc1dRblVKZTJjOFpCTFlsYUlRaEUzSGt1Uy81cm1pMjViMlFFenpFLzV6dHdiRnFxY3ZQS0dHdFo0L3ZzNVhlNndOc0w5cDM1T0xwRWNtZndnS0I0eXJlbUJpVmovY3gwODNYUk0iLCJtYWMiOiI0OTA0ODhjNGQ0NGRlNTBmZTRjOGU5OGNlYWNjZTA1NWEwYmY2Y2FkZDZlNTE5YWE2YTg3NmNjZTA3ZTYwY2VjIiwidGFnIjoiIn0%3D; expires=Fri, 30 May 2025 02:20:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkU3VU1uV1dJRWw5VUV4eTEvVkZYYkE9PSIsInZhbHVlIjoiVENMZ1U3Nnk0VDlPU2lzRklSYkJyRTc3SmNTWEJsL25ia2F1WkRhTUd4OVJkOVUySklrM3hxY21OYjdnR1Z5d01LNTNlYjZqRFRFRUxsKytwbC9yWXBBZHhZYmEzbGNjMWZhTkh5eUMrbG1QQUsrQkwrVnVnY1YzNFY4eGd2b3IiLCJtYWMiOiJiYjg2ZmUyOTIxZjY0Yjk4NjhhOWY3OTcxYWU5NDI5YWQxYjc1ZDQ5NDQ5ZDhmZTQ3OGIyODMwN2I4ZGRhNjI0IiwidGFnIjoiIn0%3D; expires=Fri, 30-May-2025 02:20:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">zapinflow_session=eyJpdiI6IlgyRHJFQW5GNTlGamF6TDlYNzhYRlE9PSIsInZhbHVlIjoiUVBmRHBrQWp1RTZWbVhOY3ppakJ0WTZrRmhGc1dRblVKZTJjOFpCTFlsYUlRaEUzSGt1Uy81cm1pMjViMlFFenpFLzV6dHdiRnFxY3ZQS0dHdFo0L3ZzNVhlNndOc0w5cDM1T0xwRWNtZndnS0I0eXJlbUJpVmovY3gwODNYUk0iLCJtYWMiOiI0OTA0ODhjNGQ0NGRlNTBmZTRjOGU5OGNlYWNjZTA1NWEwYmY2Y2FkZDZlNTE5YWE2YTg3NmNjZTA3ZTYwY2VjIiwidGFnIjoiIn0%3D; expires=Fri, 30-May-2025 02:20:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437302272\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1056315012 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://omnibis.test/telegram-messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1056315012\", {\"maxDepth\":0})</script>\n"}}