{"__meta": {"id": "Xaaa8b275ed3b66b4a0637e09cd728a7f", "datetime": "2025-05-29 23:47:09", "utime": **********.426521, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.3.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748562428.675346, "end": **********.426548, "duration": 0.7512021064758301, "duration_str": "751ms", "measures": [{"label": "Booting", "start": 1748562428.675346, "relative_start": 0, "end": **********.109746, "relative_end": **********.109746, "duration": 0.4344000816345215, "duration_str": "434ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.109778, "relative_start": 0.4344320297241211, "end": **********.426551, "relative_end": 3.0994415283203125e-06, "duration": 0.3167731761932373, "duration_str": "317ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44733024, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "livewire::simple-tailwind", "param_count": null, "params": [], "start": **********.407851, "type": "blade", "hash": "bladeD:\\laragon\\www\\omnibis\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/simple-tailwind.blade.phplivewire::simple-tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Fsimple-tailwind.blade.php&line=1", "ajax": false, "filename": "simple-tailwind.blade.php", "line": "?"}}]}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"nb_statements": 10, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0113, "accumulated_duration_str": "11.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks' limit 1", "type": "query", "params": [], "bindings": ["ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.127133, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "omnibis", "explain": null, "start_percent": 0, "width_percent": 6.726}, {"sql": "select * from `users` where `id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.364898, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omnibis", "explain": null, "start_percent": 6.726, "width_percent": 7.522}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (11) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3693058, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omnibis", "explain": null, "start_percent": 14.248, "width_percent": 8.053}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.3735821, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "omnibis", "explain": null, "start_percent": 22.301, "width_percent": 7.434}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:11', 'illuminate:cache:flexible:created:filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11", "illuminate:cache:flexible:created:filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 362}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 534}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.375904, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:387", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=387", "ajax": false, "filename": "DatabaseStore.php", "line": "387"}, "connection": "omnibis", "explain": null, "start_percent": 29.735, "width_percent": 8.938}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.388468, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "omnibis", "explain": null, "start_percent": 38.673, "width_percent": 7.522}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:11', 'illuminate:cache:flexible:created:filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11", "illuminate:cache:flexible:created:filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 362}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 534}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.390083, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:387", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=387", "ajax": false, "filename": "DatabaseStore.php", "line": "387"}, "connection": "omnibis", "explain": null, "start_percent": 46.195, "width_percent": 6.018}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 11 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' order by `created_at` desc limit 51 offset 0", "type": "query", "params": [], "bindings": ["App\\Models\\User", 11, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 2}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.3965251, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:75", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=75", "ajax": false, "filename": "DatabaseNotifications.php", "line": "75"}, "connection": "omnibis", "explain": null, "start_percent": 52.212, "width_percent": 16.637}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 11 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 11, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\notifications\\src\\/../resources/views/database-notifications.blade.php", "line": 3}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.400032, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:97", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\notifications\\src\\Livewire\\DatabaseNotifications.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=97", "ajax": false, "filename": "DatabaseNotifications.php", "line": "97"}, "connection": "omnibis", "explain": null, "start_percent": 68.85, "width_percent": 8.053}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiYWs1V1JCT2tyUnpvT2d2S3h3c2FBdUtyQ1FNc280TmNneU5LaTZZTCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjMwOiJodHRwOi8vb21uaWJpcy50ZXN0L215LXByb2ZpbGUiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfM2RjN2E5MTNlZjVmZDRiODkwZWNhYmUzNDg3MDg1NTczZTE2Y2Y4MiI7aToxMTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJHlUMEUwVnZLTGNaUGlmeGxvQkRLQmUzRHF2cHphOVd0UzUyVDY1aUY0ZDFTZ3c5NVVJbUw2IjtzOjg6ImZpbGFtZW50IjthOjA6e319', `last_activity` = **********, `user_id` = 11, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiYWs1V1JCT2tyUnpvT2d2S3h3c2FBdUtyQ1FNc280TmNneU5LaTZZTCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjMwOiJodHRwOi8vb21uaWJpcy50ZXN0L215LXByb2ZpbGUiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfM2RjN2E5MTNlZjVmZDRiODkwZWNhYmUzNDg3MDg1NTczZTE2Y2Y4MiI7aToxMTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJHlUMEUwVnZLTGNaUGlmeGxvQkRLQmUzRHF2cHphOVd0UzUyVDY1aUY0ZDFTZ3c5NVVJbUw2IjtzOjg6ImZpbGFtZW50IjthOjA6e319", **********, 11, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 176}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.4212961, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "omnibis", "explain": null, "start_percent": 76.903, "width_percent": 23.097}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"filament.livewire.database-notifications #6OcNLJTaM698pMdfNymD": "array:4 [\n  \"data\" => array:1 [\n    \"paginators\" => []\n  ]\n  \"name\" => \"filament.livewire.database-notifications\"\n  \"component\" => \"Filament\\Livewire\\DatabaseNotifications\"\n  \"id\" => \"6OcNLJTaM698pMdfNymD\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://omnibis.test/my-profile\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "11", "password_hash_web": "$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6", "filament": "[]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-197584994 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-197584994\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1268003198 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1268003198\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-699735989 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"344 characters\">{&quot;data&quot;:{&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;6OcNLJTaM698pMdfNymD&quot;,&quot;name&quot;:&quot;filament.livewire.database-notifications&quot;,&quot;path&quot;:&quot;my-profile&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;63cb12761d0047c9a9473cdf122b99d3ad5bcf7067356ffd237a57fc5c39edbc&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"248 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbW10seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6IjdGM013U3dLVVZiUjNtQjBFbFZpIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiJmNjAxMTZlNjZhODY4YWM2NDA3MTM1ODczYWQ5ZGE2MWMxYjU0NGQwNDQ3ZmUzZDM5MDRmOTg2OTIzMjM5NGRmIn0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-699735989\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-340540612 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1175 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IklUM2tlTG5UL1FqZHdYQUpFQlFCK1E9PSIsInZhbHVlIjoiNDFRVEFBanE2Y3l2WVVEU2hGUC9KTHNMWXQ1WGVkemRUck9IRDRSSHk4ZzVieVhnQWVLMzNld1p3UEdpU0lvQU0vc0Jvc3d4TjZIUEV3MVN3Q3hON1ljUm5yVTVzbGQ1V0NFcHcxdWg3TkR4cHpzOFhhbXE2eDhOaGhXOXRXT1pBWXpiSGN2bUY0b3lyeUhvM29Mam5QNUNERlRjY0Q0TXFEQWo5ajAxU2JBPSIsIm1hYyI6ImVhNjYwYjg3YzFhMTIzODg2M2ZkMjhlOTkwOTMwYWRlZDZjZDYyNDU4MTY5MGYzNzc3MzlmNGVkMmU3ODMwODUiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IkdYNHZXNmdlMVFBN0U2R0wzVHRUbmc9PSIsInZhbHVlIjoiUkQzRjh4c2JoK2pZUksvN1VIWFNPak9VMkdzK2VoMVV6bXE2S0F0UVlBeHVpTnJFSnU3TUhzTjZXcUpTMTBvZG9CMWdqWVQ4UkVlL2YwenBTemQ2VU5qeEg1bFFRSGgvek1KVnlBYWtlUG5PVHRhSkhmUVFZaDI2UWZQRnVQeDIiLCJtYWMiOiI0Zjg4MzZkNDNlMzAyYTNkYjY5MWQ0MDZiZjRmNmQyODJhYzdiNjRkNzE0ZDRiOGY1MjUzZjA2YWY0YTcyYTAxIiwidGFnIjoiIn0%3D; zapinflow_session=eyJpdiI6Ikc3TFhJRTNmNFAraElBSmhwcUpsbmc9PSIsInZhbHVlIjoiK20rQ0xpZUZKZ0UxcEQ4TXNSd0oxUmRrVmdiNmppQ0lhdS83UGI4UmZzZmxwdGF5Wm5OcHc3TmZuMXIzazFSS1RxZkFhSDltMVFCcVlsYVNzTTUxYXRUaXh4VXR3VjNja3B2ZUx1WjlyUlRxbDBvdzZoUExIOElnbklzRzl0ZkMiLCJtYWMiOiIwMDc4OGJhZDY2NThiNzEyNzMwZWMzZjE0YTdiMTE4NjlmZjRlYzI4MDk1YjIyMzRkMzhmY2RiNjczNmM2NTJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,id;q=0.8,ms;q=0.7,th;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://omnibis.test/my-profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://omnibis.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">793</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">omnibis.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-340540612\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-258721982 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"74 characters\">11|o5UxToU2uu|$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>zapinflow_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-258721982\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-195445906 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 29 May 2025 23:47:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Imk1eGRpU21oTE1oM0FxRUw1bzVJYVE9PSIsInZhbHVlIjoiWmNidW9BaSsyTnU1WG16VmJkS25ZblVqNGpJUWFNVGM3aXd5S0FaMHNzUFlYY0tkMG02TmtHdzVGdmlkMlFaZFRNZVprTVhxeGgvNHRqS2NINUV3STEvVG8weklybktQSXBCcDFIbmNkam9tcEdOMG5lTW5aeXpKL3lNVkFjaGkiLCJtYWMiOiI0MmYyMTEzM2Q4MzgwNDY0OTYzODZkM2U2MTJjZmQ5NDQ2NGQ3Y2I3OTk3MTFhZjJjNmQ5OTBlMTJlYWMwZTI1IiwidGFnIjoiIn0%3D; expires=Fri, 30 May 2025 01:47:09 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">zapinflow_session=eyJpdiI6InJRZ0tob0phVnlBN2wrNUZKRUpmOHc9PSIsInZhbHVlIjoiZWllcld1MytESGttSWN2RjZBNWNnUDArSG94dXg1V3RJSlJkRFFVOEh3VE8zVmFZdFl3N05BVUFWbnVSMEk4K2NVWUFGU3BTNm1lUkpFTEY1WUZ6VXRqSHVXQWxyYzZDRlgrK0JMYlVKanh6Z0xreHNyV0JJN3MwdDdFcWIzQXUiLCJtYWMiOiJmMmRhYTZlNDIwMzU2YTkxYTI5ZDg2ZTNjM2U4NzU1ZTgyOGUxZjZiNjJmYTQzNGFhMGFkMzEyZjgwMzJmMDBhIiwidGFnIjoiIn0%3D; expires=Fri, 30 May 2025 01:47:09 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Imk1eGRpU21oTE1oM0FxRUw1bzVJYVE9PSIsInZhbHVlIjoiWmNidW9BaSsyTnU1WG16VmJkS25ZblVqNGpJUWFNVGM3aXd5S0FaMHNzUFlYY0tkMG02TmtHdzVGdmlkMlFaZFRNZVprTVhxeGgvNHRqS2NINUV3STEvVG8weklybktQSXBCcDFIbmNkam9tcEdOMG5lTW5aeXpKL3lNVkFjaGkiLCJtYWMiOiI0MmYyMTEzM2Q4MzgwNDY0OTYzODZkM2U2MTJjZmQ5NDQ2NGQ3Y2I3OTk3MTFhZjJjNmQ5OTBlMTJlYWMwZTI1IiwidGFnIjoiIn0%3D; expires=Fri, 30-May-2025 01:47:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">zapinflow_session=eyJpdiI6InJRZ0tob0phVnlBN2wrNUZKRUpmOHc9PSIsInZhbHVlIjoiZWllcld1MytESGttSWN2RjZBNWNnUDArSG94dXg1V3RJSlJkRFFVOEh3VE8zVmFZdFl3N05BVUFWbnVSMEk4K2NVWUFGU3BTNm1lUkpFTEY1WUZ6VXRqSHVXQWxyYzZDRlgrK0JMYlVKanh6Z0xreHNyV0JJN3MwdDdFcWIzQXUiLCJtYWMiOiJmMmRhYTZlNDIwMzU2YTkxYTI5ZDg2ZTNjM2U4NzU1ZTgyOGUxZjZiNjJmYTQzNGFhMGFkMzEyZjgwMzJmMDBhIiwidGFnIjoiIn0%3D; expires=Fri, 30-May-2025 01:47:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-195445906\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-425981379 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://omnibis.test/my-profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-425981379\", {\"maxDepth\":0})</script>\n"}}