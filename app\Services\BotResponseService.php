<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\Message;
use App\Models\Order;
use App\Models\Product;
use App\Models\OrderPayment;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BotResponseService
{
    /**
     * Process incoming message and generate appropriate response
     */
    public function processMessage(Message $message, Customer $customer): array
    {
        $messageText = strtolower(trim($message->message));
        
        // Check for specific commands/keywords
        if ($this->isGreeting($messageText)) {
            return $this->handleGreeting($customer);
        }
        
        if ($this->isProductInquiry($messageText)) {
            return $this->handleProductInquiry($messageText);
        }
        
        if ($this->isOrderInquiry($messageText)) {
            return $this->handleOrderInquiry($customer, $messageText);
        }
        
        if ($this->isOrderRequest($messageText)) {
            return $this->handleOrderRequest($customer, $messageText);
        }
        
        // Default response
        return $this->handleDefaultResponse();
    }

    /**
     * Send order status update notification to customer
     */
    public function sendOrderStatusUpdate(Order $order): void
    {
        $customer = $order->customer;
        $product = $order->product;
        
        $message = $this->generateOrderStatusMessage($order, $product);
        
        $this->sendMessage($customer, $message, $order->account);
    }

    /**
     * Check if message is a greeting
     */
    private function isGreeting(string $message): bool
    {
        $greetings = ['hi', 'hello', 'halo', 'hai', 'selamat', 'good morning', 'good afternoon', 'good evening'];
        
        foreach ($greetings as $greeting) {
            if (str_contains($message, $greeting)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if message is a product inquiry
     */
    private function isProductInquiry(string $message): bool
    {
        $keywords = ['produk', 'product', 'katalog', 'catalog', 'harga', 'price', 'stok', 'stock'];
        
        foreach ($keywords as $keyword) {
            if (str_contains($message, $keyword)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if message is an order inquiry
     */
    private function isOrderInquiry(string $message): bool
    {
        $keywords = ['pesanan', 'order', 'status', 'tracking', 'pengiriman', 'delivery'];
        
        foreach ($keywords as $keyword) {
            if (str_contains($message, $keyword)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if message is an order request
     */
    private function isOrderRequest(string $message): bool
    {
        $keywords = ['pesan', 'beli', 'buy', 'order', 'mau', 'want'];
        
        foreach ($keywords as $keyword) {
            if (str_contains($message, $keyword)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Handle greeting messages
     */
    private function handleGreeting(Customer $customer): array
    {
        $response = "Halo {$customer->display_name}! 👋\n\n";
        $response .= "Selamat datang di toko kami! Saya adalah asisten virtual yang siap membantu Anda.\n\n";
        $response .= "Apa yang bisa saya bantu hari ini?\n";
        $response .= "• Lihat produk: ketik 'produk' atau 'katalog'\n";
        $response .= "• Cek pesanan: ketik 'pesanan' atau 'order'\n";
        $response .= "• Bantuan lainnya: ketik 'bantuan' atau 'help'";

        return [
            'type' => 'greeting',
            'message' => $response,
            'action' => 'send_message'
        ];
    }

    /**
     * Handle product inquiry
     */
    private function handleProductInquiry(string $message): array
    {
        $products = Product::active()->inStock()->take(5)->get();
        
        if ($products->isEmpty()) {
            $response = "Maaf, saat ini tidak ada produk yang tersedia. 😔";
        } else {
            $response = "🛍️ *Produk Tersedia:*\n\n";
            
            foreach ($products as $product) {
                $response .= "📦 *{$product->name}*\n";
                $response .= "💰 Harga: {$product->formatted_price}\n";
                $response .= "📊 Stok: {$product->quantity} unit\n";
                if ($product->description) {
                    $response .= "📝 {$product->description}\n";
                }
                $response .= "\n";
            }
            
            $response .= "Untuk memesan, ketik: *pesan [nama produk]*\n";
            $response .= "Contoh: pesan {$products->first()->name}";
        }

        return [
            'type' => 'product_list',
            'message' => $response,
            'action' => 'send_message',
            'data' => $products->toArray()
        ];
    }

    /**
     * Handle order inquiry
     */
    private function handleOrderInquiry(Customer $customer, string $message): array
    {
        $orders = $customer->orders()->latest()->take(3)->with('product')->get();
        
        if ($orders->isEmpty()) {
            $response = "Anda belum memiliki pesanan. 📦\n\n";
            $response .= "Untuk melihat produk yang tersedia, ketik 'produk' atau 'katalog'.";
        } else {
            $response = "📋 *Pesanan Anda:*\n\n";
            
            foreach ($orders as $order) {
                $response .= "🔖 Order #{$order->id}\n";
                $response .= "📦 Produk: {$order->product->name}\n";
                $response .= "💰 Total: {$order->formatted_total_amount}\n";
                $response .= "📊 Status: {$order->status_display}\n";
                $response .= "📅 Tanggal: {$order->created_at->format('d/m/Y H:i')}\n\n";
            }
            
            $response .= "Untuk detail pesanan tertentu, ketik: *detail order [ID]*";
        }

        return [
            'type' => 'order_status',
            'message' => $response,
            'action' => 'send_message',
            'data' => $orders->toArray()
        ];
    }

    /**
     * Handle order request
     */
    private function handleOrderRequest(Customer $customer, string $message): array
    {
        // Extract product name from message
        $productName = $this->extractProductName($message);
        
        if (!$productName) {
            $response = "Mohon sebutkan nama produk yang ingin dipesan.\n\n";
            $response .= "Contoh: *pesan Produk A*\n\n";
            $response .= "Untuk melihat daftar produk, ketik 'produk'.";
            
            return [
                'type' => 'order_help',
                'message' => $response,
                'action' => 'send_message'
            ];
        }
        
        $product = Product::where('name', 'LIKE', "%{$productName}%")
                          ->active()
                          ->inStock()
                          ->first();
        
        if (!$product) {
            $response = "Maaf, produk '{$productName}' tidak ditemukan atau sedang tidak tersedia. 😔\n\n";
            $response .= "Untuk melihat produk yang tersedia, ketik 'produk'.";
            
            return [
                'type' => 'product_not_found',
                'message' => $response,
                'action' => 'send_message'
            ];
        }
        
        $response = "✅ Produk ditemukan!\n\n";
        $response .= "📦 *{$product->name}*\n";
        $response .= "💰 Harga: {$product->formatted_price}\n";
        $response .= "📊 Stok tersedia: {$product->quantity} unit\n\n";
        $response .= "Untuk melanjutkan pemesanan, silakan konfirmasi:\n";
        $response .= "*Ya, pesan {$product->name}*";

        return [
            'type' => 'order_confirmation',
            'message' => $response,
            'action' => 'send_message',
            'data' => [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'price' => $product->price
            ]
        ];
    }

    /**
     * Handle default response
     */
    private function handleDefaultResponse(): array
    {
        $response = "Maaf, saya tidak mengerti pesan Anda. 🤔\n\n";
        $response .= "Berikut adalah hal-hal yang bisa saya bantu:\n";
        $response .= "• Ketik 'produk' untuk melihat katalog\n";
        $response .= "• Ketik 'pesanan' untuk cek status order\n";
        $response .= "• Ketik 'bantuan' untuk panduan lengkap\n\n";
        $response .= "Atau Anda bisa langsung menanyakan tentang produk tertentu!";

        return [
            'type' => 'help',
            'message' => $response,
            'action' => 'send_message'
        ];
    }

    /**
     * Generate order status update message
     */
    private function generateOrderStatusMessage(Order $order, Product $product): string
    {
        $message = "📦 *Update Pesanan*\n\n";
        $message .= "🔖 Order #{$order->id}\n";
        $message .= "📦 Produk: {$product->name}\n";
        $message .= "📊 Status: *{$order->status_display}*\n";
        $message .= "📅 Update: " . now()->format('d/m/Y H:i') . "\n\n";
        
        switch ($order->status) {
            case 'pending':
                $message .= "⏳ Pesanan Anda sedang diproses.";
                break;
            case 'shipped':
                $message .= "🚚 Pesanan Anda sedang dalam perjalanan!";
                break;
            case 'delivered':
                $message .= "✅ Pesanan Anda telah sampai! Terima kasih telah berbelanja.";
                break;
        }

        return $message;
    }

    /**
     * Extract product name from order message
     */
    private function extractProductName(string $message): ?string
    {
        // Remove common order keywords
        $keywords = ['pesan', 'beli', 'buy', 'order', 'mau', 'want'];
        $cleanMessage = $message;
        
        foreach ($keywords as $keyword) {
            $cleanMessage = str_replace($keyword, '', $cleanMessage);
        }
        
        return trim($cleanMessage) ?: null;
    }

    /**
     * Send message to customer via n8n webhook
     */
    private function sendMessage(Customer $customer, string $message, $account): void
    {
        try {
            // This would integrate with your n8n webhook for sending messages
            $webhookUrl = config('services.n8n.webhook_url');
            
            if (!$webhookUrl) {
                Log::warning('N8N webhook URL not configured');
                return;
            }
            
            $payload = [
                'account_id' => $account->id,
                'recipient' => $customer->account_type === 'whatsapp' 
                    ? $customer->phone_number 
                    : $customer->chat_id,
                'message' => $message,
                'channel' => $customer->account_type,
            ];
            
            Http::post($webhookUrl, $payload);
            
            // Store outgoing message
            Message::create([
                'account_id' => $account->id,
                'sender' => $customer->account_type === 'whatsapp' 
                    ? $customer->phone_number 
                    : $customer->chat_id,
                'message' => $message,
                'timestamp' => now(),
                'channel' => $customer->account_type,
                'direction' => 'outgoing',
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send message via n8n', [
                'error' => $e->getMessage(),
                'customer_id' => $customer->id
            ]);
        }
    }
}
