<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\WebhookController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PaymentController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::post('/payment/create', [PaymentController::class, 'createPayment'])->name('payment.create');

Route::post('/payment/notify', [PaymentController::class, 'notify'])->name('payment.notify');

// Webhook routes for n8n integration
Route::prefix('webhooks')->group(function () {
    Route::post('/whatsapp', [WebhookController::class, 'whatsappWebhook'])->name('webhooks.whatsapp');
    Route::post('/telegram', [WebhookController::class, 'telegramWebhook'])->name('webhooks.telegram');
    Route::post('/order-status', [WebhookController::class, 'orderStatusWebhook'])->name('webhooks.order-status');
});

// Route::post('/login', [AuthController::class, 'login']);
