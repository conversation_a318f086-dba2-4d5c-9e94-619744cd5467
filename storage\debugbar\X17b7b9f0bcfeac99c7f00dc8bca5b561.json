{"__meta": {"id": "X17b7b9f0bcfeac99c7f00dc8bca5b561", "datetime": "2025-05-30 00:00:21", "utime": 1748563221.540463, "method": "GET", "uri": "/livewire/livewire.js?id=38dc8241", "ip": "127.0.0.1"}, "php": {"version": "8.3.6", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748563220.989985, "end": 1748563221.540498, "duration": 0.5505130290985107, "duration_str": "551ms", "measures": [{"label": "Booting", "start": 1748563220.989985, "relative_start": 0, "end": 1748563221.331108, "relative_end": 1748563221.331108, "duration": 0.341123104095459, "duration_str": "341ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1748563221.331122, "relative_start": 0.3411369323730469, "end": 1748563221.540503, "relative_end": 5.0067901611328125e-06, "duration": 0.209381103515625, "duration_str": "209ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44205144, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/livewire/livewire.js", "status_code": "<pre class=sf-dump id=sf-dump-1015994729 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1015994729\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/javascript; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1545956273 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">38dc8241</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1545956273\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1877399579 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1877399579\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1756750402 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,id;q=0.8,ms;q=0.7,th;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2275 characters\">_ga=GA1.1.1807928002.1744424787; _ga_GX4PMQ33P2=GS1.1.1744424786.1.1.1744424877.0.0.0; ajs_anonymous_id=d96172e1-0e39-43ac-9ce4-c6de1b144fbf; ajs_user_id=ddff98b5-2489-59a7-99e6-b02f9d0cf101; rl_page_init_referrer=RudderEncrypt%3AU2FsdGVkX19bE8hQomva2sEBnL4S%2BYkVCc2S5TzDvVU%3D; rl_page_init_referring_domain=RudderEncrypt%3AU2FsdGVkX19nUIHJwXmOevvGOLhi4or30YX2bruUA%2Fw%3D; rl_anonymous_id=RudderEncrypt%3AU2FsdGVkX19CO5ClbeVZj0E3JEchF2DyF5LPzrKEmh%2Brnnzxrr%2BlymxSvE0cLQlHQKRzouOBNUhe%2FLDDZd08Yw%3D%3D; rl_user_id=RudderEncrypt%3AU2FsdGVkX1%2FCQxU4nm%2FmFu8DueBBKO3RgBfFCsRLjsF4aZmb7Bvu%2BlEisG%2B56GvR0kymj9SfCn1OhV%2B0ebdjTTZ6V6Yjn4q6cHpBu2DCdFGtnGWDOcHiv5C%2BofQwheVOWIMMnwiZXshM%2FcCZmCmZpbRjP0MvIw0AhJd%2FX3UWmtw%3D; rl_trait=RudderEncrypt%3AU2FsdGVkX19C5Sghj78lAY3dwXT32mRfBgROmENmQJxeF4%2BNbEF%2BZryQWR604OqIYKi%2FAn%2FVqWdlzPVvKJOfRQn1ba%2BML2dhJ4B63sPdcle2tql4w7V1Zpvr%2F0eINK5K1ji3w6c5NUKah5Jvlnp2kg%3D%3D; rl_session=RudderEncrypt%3AU2FsdGVkX1%2BTYu8iuWofVyMZZp8VAQB6Laz2UNL3gCDgHDGHnj2m%2FPEVDjFKBdXc%2FkWQmktMDVF%2B0sKsEeYlQdmLOs6UcGAOXjlmez2txsPumYRyazmW4x8AF%2BqhSSbI6WsUca%2FKB%2BTvnn1dsk6wbw%3D%3D; ph_phc_4URIAm1uYfJO7j8kWSe0J8lc8IqnstRLS7Jx8NcakHo_posthog=%7B%22distinct_id%22%3A%22ccaec6db7babedd35c05cbad7e533f9e7f7540de7813cf61f53d9080e928c67c%23aaef915d-d89d-4a1d-9bbb-053762f7555c%22%2C%22%24sesid%22%3A%5B1746946384895%2C%220196be1e-63ff-70a4-9075-20dc490e664c%22%2C1746946384895%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A5678%2Fsetup%22%7D%7D; XSRF-TOKEN=eyJpdiI6IkR2dU1TUDhMcU54Z3ludGxWZ0dEeUE9PSIsInZhbHVlIjoidEVqWjRGSng0OG85Ulo4STN4U3l6ZHJjZ1BVZDVsZVd4YnZhOEF2ZCs5c2lZTUZPak90YWtHTmdneG9FWGJtK3hQa2NvdHd5TmJyVmxkbXFmbk1GSHJsL2cyN2N4ZGhOcitGbW55citTbnVxdnZzNWcraFgycXhKVHhOM0MxdzAiLCJtYWMiOiIyNDY1MzA3MDE1Mzk0Y2FhYjhkYzg4ZDU2ZWFmMjljNGYwNGJlMzFkMzg3OWEyYTQ0ODIyZGVlNjEwODI3ZGNmIiwidGFnIjoiIn0%3D; zapinflow_session=eyJpdiI6IkdSVUd2NnRCYk5EQTdmWDFvUkxnNnc9PSIsInZhbHVlIjoiTU9OWGM2YXUwNklEdGZUOExVZklPVjJCWXA3RUY5Q1poQy9kOVVqaWJsZXhBeEN4TFRMRTNOQWFFSTRFbzdGNFJhRWkrUndBMlZwcXFyU0hsUWNqY1MveDFhN1EvSUk5UUNrczAwNWE5cGZVSTM4RFp4aC9UMnFrcVN2WENpM2EiLCJtYWMiOiIwZjQ5ZGQ2ZmQwNTVmNTRjZjg1ZGM4ZDU5ODdlZGI4ZDE2OGViNTM4NjVjNTJmODFkMDViYzc5YmUwYjVhNjE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1756750402\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-783764579 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => \"<span class=sf-dump-str title=\"27 characters\">GA1.1.1807928002.1744424787</span>\"\n  \"<span class=sf-dump-key>_ga_GX4PMQ33P2</span>\" => \"<span class=sf-dump-str title=\"37 characters\">GS1.1.1744424786.1.1.1744424877.0.0.0</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d96172e1-0e39-43ac-9ce4-c6de1b144fbf</span>\"\n  \"<span class=sf-dump-key>ajs_user_id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ddff98b5-2489-59a7-99e6-b02f9d0cf101</span>\"\n  \"<span class=sf-dump-key>rl_page_init_referrer</span>\" => \"<span class=sf-dump-str title=\"58 characters\">RudderEncrypt:U2FsdGVkX19bE8hQomva2sEBnL4S+YkVCc2S5TzDvVU=</span>\"\n  \"<span class=sf-dump-key>rl_page_init_referring_domain</span>\" => \"<span class=sf-dump-str title=\"58 characters\">RudderEncrypt:U2FsdGVkX19nUIHJwXmOevvGOLhi4or30YX2bruUA/w=</span>\"\n  \"<span class=sf-dump-key>rl_anonymous_id</span>\" => \"<span class=sf-dump-str title=\"102 characters\">RudderEncrypt:U2FsdGVkX19CO5ClbeVZj0E3JEchF2DyF5LPzrKEmh+rnnzxrr+lymxSvE0cLQlHQKRzouOBNUhe/LDDZd08Yw==</span>\"\n  \"<span class=sf-dump-key>rl_user_id</span>\" => \"<span class=sf-dump-str title=\"186 characters\">RudderEncrypt:U2FsdGVkX1/CQxU4nm/mFu8DueBBKO3RgBfFCsRLjsF4aZmb7Bvu+lEisG+56GvR0kymj9SfCn1OhV+0ebdjTTZ6V6Yjn4q6cHpBu2DCdFGtnGWDOcHiv5C+ofQwheVOWIMMnwiZXshM/cCZmCmZpbRjP0MvIw0AhJd/X3UWmtw=</span>\"\n  \"<span class=sf-dump-key>rl_trait</span>\" => \"<span class=sf-dump-str title=\"166 characters\">RudderEncrypt:U2FsdGVkX19C5Sghj78lAY3dwXT32mRfBgROmENmQJxeF4+NbEF+ZryQWR604OqIYKi/An/VqWdlzPVvKJOfRQn1ba+ML2dhJ4B63sPdcle2tql4w7V1Zpvr/0eINK5K1ji3w6c5NUKah5Jvlnp2kg==</span>\"\n  \"<span class=sf-dump-key>rl_session</span>\" => \"<span class=sf-dump-str title=\"166 characters\">RudderEncrypt:U2FsdGVkX1+TYu8iuWofVyMZZp8VAQB6Laz2UNL3gCDgHDGHnj2m/PEVDjFKBdXc/kWQmktMDVF+0sKsEeYlQdmLOs6UcGAOXjlmez2txsPumYRyazmW4x8AF+qhSSbI6WsUca/KB+Tvnn1dsk6wbw==</span>\"\n  \"<span class=sf-dump-key>ph_phc_4URIAm1uYfJO7j8kWSe0J8lc8IqnstRLS7Jx8NcakHo_posthog</span>\" => \"<span class=sf-dump-str title=\"282 characters\">{&quot;distinct_id&quot;:&quot;ccaec6db7babedd35c05cbad7e533f9e7f7540de7813cf61f53d9080e928c67c#aaef915d-d89d-4a1d-9bbb-053762f7555c&quot;,&quot;$sesid&quot;:[1746946384895,&quot;0196be1e-63ff-70a4-9075-20dc490e664c&quot;,1746946384895],&quot;$epp&quot;:true,&quot;$initial_person_info&quot;:{&quot;r&quot;:&quot;$direct&quot;,&quot;u&quot;:&quot;http://localhost:5678/setup&quot;}}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkR2dU1TUDhMcU54Z3ludGxWZ0dEeUE9PSIsInZhbHVlIjoidEVqWjRGSng0OG85Ulo4STN4U3l6ZHJjZ1BVZDVsZVd4YnZhOEF2ZCs5c2lZTUZPak90YWtHTmdneG9FWGJtK3hQa2NvdHd5TmJyVmxkbXFmbk1GSHJsL2cyN2N4ZGhOcitGbW55citTbnVxdnZzNWcraFgycXhKVHhOM0MxdzAiLCJtYWMiOiIyNDY1MzA3MDE1Mzk0Y2FhYjhkYzg4ZDU2ZWFmMjljNGYwNGJlMzFkMzg3OWEyYTQ0ODIyZGVlNjEwODI3ZGNmIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>zapinflow_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkdSVUd2NnRCYk5EQTdmWDFvUkxnNnc9PSIsInZhbHVlIjoiTU9OWGM2YXUwNklEdGZUOExVZklPVjJCWXA3RUY5Q1poQy9kOVVqaWJsZXhBeEN4TFRMRTNOQWFFSTRFbzdGNFJhRWkrUndBMlZwcXFyU0hsUWNqY1MveDFhN1EvSUk5UUNrczAwNWE5cGZVSTM4RFp4aC9UMnFrcVN2WENpM2EiLCJtYWMiOiIwZjQ5ZGQ2ZmQwNTVmNTRjZjg1ZGM4ZDU5ODdlZGI4ZDE2OGViNTM4NjVjNTJmODFkMDViYzc5YmUwYjVhNjE0IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-783764579\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1110538676 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 30 May 2026 00:00:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Oct 2024 19:35:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 30 May 2025 00:00:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">340160</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1110538676\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1806975486 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1806975486\", {\"maxDepth\":0})</script>\n"}}