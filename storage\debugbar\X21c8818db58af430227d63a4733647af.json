{"__meta": {"id": "X21c8818db58af430227d63a4733647af", "datetime": "2025-05-29 23:38:47", "utime": **********.29512, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.3.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748561926.511106, "end": **********.295145, "duration": 0.7840390205383301, "duration_str": "784ms", "measures": [{"label": "Booting", "start": 1748561926.511106, "relative_start": 0, "end": 1748561926.911443, "relative_end": 1748561926.911443, "duration": 0.40033698081970215, "duration_str": "400ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1748561926.91146, "relative_start": 0.40035390853881836, "end": **********.295148, "relative_end": 2.86102294921875e-06, "duration": 0.38368797302246094, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45627784, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\ProductResource\\Pages\\CreateProduct@create", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FCreateRecord.php&line=77\" onclick=\"\">vendor/filament/filament/src/Resources/Pages/CreateRecord.php:77-130</a>"}, "queries": {"nb_statements": 9, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.010599999999999998, "accumulated_duration_str": "10.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks' limit 1", "type": "query", "params": [], "bindings": ["ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": 1748561926.929674, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "omnibis", "explain": null, "start_percent": 0, "width_percent": 10.377}, {"sql": "select * from `users` where `id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1283941, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omnibis", "explain": null, "start_percent": 10.377, "width_percent": 9.245}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (11) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1321728, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omnibis", "explain": null, "start_percent": 19.623, "width_percent": 10.472}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.1371279, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "omnibis", "explain": null, "start_percent": 30.094, "width_percent": 7.075}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:11', 'illuminate:cache:flexible:created:filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11", "illuminate:cache:flexible:created:filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 362}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 534}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.1391928, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:387", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=387", "ajax": false, "filename": "DatabaseStore.php", "line": "387"}, "connection": "omnibis", "explain": null, "start_percent": 37.17, "width_percent": 9.057}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.169465, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "omnibis", "explain": null, "start_percent": 46.226, "width_percent": 6.604}, {"sql": "select count(*) as aggregate from `products` where `code` = 'bail10'", "type": "query", "params": [], "bindings": ["bail10"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 682}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 477}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 512}], "start": **********.199578, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "omnibis", "explain": null, "start_percent": 52.83, "width_percent": 12.453}, {"sql": "insert into `products` (`code`, `name`, `price`, `currency`, `description`, `image`, `quantity`, `status`, `updated_at`, `created_at`) values ('bail10', 'Bilee', '10000', 'IDR', 'aaa', null, '10', 1, '2025-05-29 23:38:47', '2025-05-29 23:38:47')", "type": "query", "params": [], "bindings": ["bail10", "<PERSON><PERSON><PERSON>", "10000", "IDR", "aaa", null, "10", 1, "2025-05-29 23:38:47", "2025-05-29 23:38:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\filament\\src\\Resources\\Pages\\CreateRecord.php", "line": 177}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\filament\\src\\Resources\\Pages\\CreateRecord.php", "line": 94}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.207393, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "CreateRecord.php:177", "source": {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\filament\\src\\Resources\\Pages\\CreateRecord.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FCreateRecord.php&line=177", "ajax": false, "filename": "CreateRecord.php", "line": "177"}, "connection": "omnibis", "explain": null, "start_percent": 65.283, "width_percent": 16.415}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiYWs1V1JCT2tyUnpvT2d2S3h3c2FBdUtyQ1FNc280TmNneU5LaTZZTCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM1OiJodHRwOi8vb21uaWJpcy50ZXN0L3Byb2R1Y3RzL2NyZWF0ZSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjExO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkeVQwRTBWdktMY1pQaWZ4bG9CREtCZTNEcXZwemE5V3RTNTJUNjVpRjRkMVNndzk1VUltTDYiO3M6ODoiZmlsYW1lbnQiO2E6MTp7czoxMzoibm90aWZpY2F0aW9ucyI7YToxOntpOjA7YToxMTp7czoyOiJpZCI7czozNjoiOWYwN2UxNDEtMTZmZi00M2E3LTg1MWQtOTY2NGJhN2YyMDY0IjtzOjc6ImFjdGlvbnMiO2E6MDp7fXM6NDoiYm9keSI7TjtzOjU6ImNvbG9yIjtOO3M6ODoiZHVyYXRpb24iO2k6NjAwMDtzOjQ6Imljb24iO3M6MjM6Imhlcm9pY29uLW8tY2hlY2stY2lyY2xlIjtzOjk6Imljb25Db2xvciI7czo3OiJzdWNjZXNzIjtzOjY6InN0YXR1cyI7czo3OiJzdWNjZXNzIjtzOjU6InRpdGxlIjtzOjc6IkNyZWF0ZWQiO3M6NDoidmlldyI7czozNjoiZmlsYW1lbnQtbm90aWZpY2F0aW9uczo6bm90aWZpY2F0aW9uIjtzOjg6InZpZXdEYXRhIjthOjA6e319fX19', `last_activity` = **********, `user_id` = 11, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiYWs1V1JCT2tyUnpvT2d2S3h3c2FBdUtyQ1FNc280TmNneU5LaTZZTCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM1OiJodHRwOi8vb21uaWJpcy50ZXN0L3Byb2R1Y3RzL2NyZWF0ZSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjExO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkeVQwRTBWdktMY1pQaWZ4bG9CREtCZTNEcXZwemE5V3RTNTJUNjVpRjRkMVNndzk1VUltTDYiO3M6ODoiZmlsYW1lbnQiO2E6MTp7czoxMzoibm90aWZpY2F0aW9ucyI7YToxOntpOjA7YToxMTp7czoyOiJpZCI7czozNjoiOWYwN2UxNDEtMTZmZi00M2E3LTg1MWQtOTY2NGJhN2YyMDY0IjtzOjc6ImFjdGlvbnMiO2E6MDp7fXM6NDoiYm9keSI7TjtzOjU6ImNvbG9yIjtOO3M6ODoiZHVyYXRpb24iO2k6NjAwMDtzOjQ6Imljb24iO3M6MjM6Imhlcm9pY29uLW8tY2hlY2stY2lyY2xlIjtzOjk6Imljb25Db2xvciI7czo3OiJzdWNjZXNzIjtzOjY6InN0YXR1cyI7czo3OiJzdWNjZXNzIjtzOjU6InRpdGxlIjtzOjc6IkNyZWF0ZWQiO3M6NDoidmlldyI7czozNjoiZmlsYW1lbnQtbm90aWZpY2F0aW9uczo6bm90aWZpY2F0aW9uIjtzOjg6InZpZXdEYXRhIjthOjA6e319fX19", **********, 11, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 176}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.290296, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "omnibis", "explain": null, "start_percent": 81.698, "width_percent": 18.302}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://omnibis.test/products/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "11", "password_hash_web": "$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6", "filament": "array:1 [\n  \"notifications\" => array:1 [\n    0 => array:11 [\n      \"id\" => \"9f07e141-16ff-43a7-851d-9664ba7f2064\"\n      \"actions\" => []\n      \"body\" => null\n      \"color\" => null\n      \"duration\" => 6000\n      \"icon\" => \"heroicon-o-check-circle\"\n      \"iconColor\" => \"success\"\n      \"status\" => \"success\"\n      \"title\" => \"Created\"\n      \"view\" => \"filament-notifications::notification\"\n      \"viewData\" => []\n    ]\n  ]\n]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-455224122 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-455224122\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-705571079 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-705571079\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-111510920 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1118 characters\">{&quot;data&quot;:{&quot;record&quot;:null,&quot;data&quot;:[{&quot;code&quot;:null,&quot;name&quot;:null,&quot;price&quot;:null,&quot;currency&quot;:&quot;IDR&quot;,&quot;description&quot;:null,&quot;image&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;quantity&quot;:0,&quot;status&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],&quot;previousUrl&quot;:&quot;http:\\/\\/omnibis.test\\/products&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;savedDataHash&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;yZjgWTsPkrOthU9B57KR&quot;,&quot;name&quot;:&quot;app.filament.resources.product-resource.pages.create-product&quot;,&quot;path&quot;:&quot;products\\/create&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;955404f279780f8237af048b4eae8d1068f3ca90add40f1a11adffe0b02fddeb&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data.code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">bail10</span>\"\n        \"<span class=sf-dump-key>data.name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Bilee</span>\"\n        \"<span class=sf-dump-key>data.price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10000</span>\"\n        \"<span class=sf-dump-key>data.description</span>\" => \"<span class=sf-dump-str title=\"3 characters\">aaa</span>\"\n        \"<span class=sf-dump-key>data.quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-111510920\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1921544523 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1175 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IklUM2tlTG5UL1FqZHdYQUpFQlFCK1E9PSIsInZhbHVlIjoiNDFRVEFBanE2Y3l2WVVEU2hGUC9KTHNMWXQ1WGVkemRUck9IRDRSSHk4ZzVieVhnQWVLMzNld1p3UEdpU0lvQU0vc0Jvc3d4TjZIUEV3MVN3Q3hON1ljUm5yVTVzbGQ1V0NFcHcxdWg3TkR4cHpzOFhhbXE2eDhOaGhXOXRXT1pBWXpiSGN2bUY0b3lyeUhvM29Mam5QNUNERlRjY0Q0TXFEQWo5ajAxU2JBPSIsIm1hYyI6ImVhNjYwYjg3YzFhMTIzODg2M2ZkMjhlOTkwOTMwYWRlZDZjZDYyNDU4MTY5MGYzNzc3MzlmNGVkMmU3ODMwODUiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IkZ2Qk5uSFFsanNXQjErLzBxTllwWEE9PSIsInZhbHVlIjoiVWtOMVBseXdZTEZsN3BWblVmNElVdHlSaHFGRUtLNmNoK2V6TXdnT3QxbDFESFo1TXcvREV6TE5JZmE5c2JoZU91MHBBNDJsenBHYjJXWGRXN3BpSGo4Q28zS1dwS0FrUXZVTFNYRlhrOC9BOUFUNTN3NE4vRjRLZ08rWG5XNkEiLCJtYWMiOiJlYzE4YTYxOGM5NGIzZWNhYmRkMWZhMjU3NzNiODAyYzQ3MTkyOWMwNGEwZjJmODIzOGRjMjNlZWFjODE3ZDY4IiwidGFnIjoiIn0%3D; zapinflow_session=eyJpdiI6IlJGczRhUGw0bkVOYjI5V1NLTEVqdEE9PSIsInZhbHVlIjoiUk50aUZmU0dDVGxNK3dkS0owSU1sQmNsZklpYXI0SjJOSy81VWFYUWZ1Z01Ta2ExSjZYRUVrVjhWSllsbndMSXVxVDVvSXdiL1ExTjBGZlk1SlpuQWVsRUlvSVRvVXgzeHAwU0x3VDZVU2RCNmNja05pQzFaaTFXNHh1Q1RqK00iLCJtYWMiOiJkZDFkNGM3YmQyNjExMGRjZTZmMzgzYTAzNDE2ZThmMTEyZjYwMzFkZDMwMmNiMjM3YmRmNTExNTNmNjU1YWFmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,id;q=0.8,ms;q=0.7,th;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://omnibis.test/products/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://omnibis.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1518</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">omnibis.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921544523\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1020872492 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"74 characters\">11|o5UxToU2uu|$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>zapinflow_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020872492\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1968679161 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 29 May 2025 23:38:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlBQNmR1S1YxS0tSbFJCZFY5U2xmMlE9PSIsInZhbHVlIjoiRFpuZEd3aS9CbGwvZUIvMitTa0VCZkkzN1RhbUt5OVBiRGlnZXRSVTJZQzR2ZjFHQUJHTVJKZHlZeHNoQ0h6MWd1RXZBZWs5ak5YdHl1MTZubHpyRWZERWt6ZHYxdGNLTjErRTRxaXlJTWVOdEszbnJpN2VYUENWTzRtSzFlL2kiLCJtYWMiOiIxZjBhOGFjOTQ1NzAwNGM4YmZiZTQ2YWQxMWIyOWZjZWE4ZWZiNmE4N2E1OTIyM2Y3ZjRiY2MzNjI0YTYyNjYyIiwidGFnIjoiIn0%3D; expires=Fri, 30 May 2025 01:38:47 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">zapinflow_session=eyJpdiI6Imk0NjQvOXJyRitSWHBxbzE3Q20xWGc9PSIsInZhbHVlIjoiejdqbUIzVlRkdHByQzJMTm1CMzBRQ2JSVnhFdW90a0N2aDJVTzQ4eUxZSGJtb05TSlJ1ckQrK25DQkFTbmZoc1NkTThxME9IOGF6WWs3bmxDaktIV2tyYkp6UHVXWFEvc21hSDRxZk5JMDIzMGorZGo2OW4rT1k2amVxc0laSHgiLCJtYWMiOiIxOTU5MjI5MGJkZmM4ZWM2NzdmZmQ3NzVlNjlhYTNjYWE2NTU3ZjZmMTI0ZDZkN2I1NGRiNjFmM2IzOGY1ZjgxIiwidGFnIjoiIn0%3D; expires=Fri, 30 May 2025 01:38:47 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlBQNmR1S1YxS0tSbFJCZFY5U2xmMlE9PSIsInZhbHVlIjoiRFpuZEd3aS9CbGwvZUIvMitTa0VCZkkzN1RhbUt5OVBiRGlnZXRSVTJZQzR2ZjFHQUJHTVJKZHlZeHNoQ0h6MWd1RXZBZWs5ak5YdHl1MTZubHpyRWZERWt6ZHYxdGNLTjErRTRxaXlJTWVOdEszbnJpN2VYUENWTzRtSzFlL2kiLCJtYWMiOiIxZjBhOGFjOTQ1NzAwNGM4YmZiZTQ2YWQxMWIyOWZjZWE4ZWZiNmE4N2E1OTIyM2Y3ZjRiY2MzNjI0YTYyNjYyIiwidGFnIjoiIn0%3D; expires=Fri, 30-May-2025 01:38:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">zapinflow_session=eyJpdiI6Imk0NjQvOXJyRitSWHBxbzE3Q20xWGc9PSIsInZhbHVlIjoiejdqbUIzVlRkdHByQzJMTm1CMzBRQ2JSVnhFdW90a0N2aDJVTzQ4eUxZSGJtb05TSlJ1ckQrK25DQkFTbmZoc1NkTThxME9IOGF6WWs3bmxDaktIV2tyYkp6UHVXWFEvc21hSDRxZk5JMDIzMGorZGo2OW4rT1k2amVxc0laSHgiLCJtYWMiOiIxOTU5MjI5MGJkZmM4ZWM2NzdmZmQ3NzVlNjlhYTNjYWE2NTU3ZjZmMTI0ZDZkN2I1NGRiNjFmM2IzOGY1ZjgxIiwidGFnIjoiIn0%3D; expires=Fri, 30-May-2025 01:38:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968679161\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-57203862 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://omnibis.test/products/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>notifications</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f07e141-16ff-43a7-851d-9664ba7f2064</span>\"\n        \"<span class=sf-dump-key>actions</span>\" => []\n        \"<span class=sf-dump-key>body</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>6000</span>\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"23 characters\">heroicon-o-check-circle</span>\"\n        \"<span class=sf-dump-key>iconColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Created</span>\"\n        \"<span class=sf-dump-key>view</span>\" => \"<span class=sf-dump-str title=\"36 characters\">filament-notifications::notification</span>\"\n        \"<span class=sf-dump-key>viewData</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57203862\", {\"maxDepth\":0})</script>\n"}}