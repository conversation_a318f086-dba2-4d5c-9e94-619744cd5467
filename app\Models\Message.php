<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Message extends Model
{
    use HasFactory;

    protected $fillable = [
        'account_id',
        'sender',
        'message',
        'timestamp',
        'channel',
        'direction',
        'message_id',
        'metadata',
    ];

    protected $casts = [
        'timestamp' => 'datetime',
        'metadata' => 'array',
    ];

    // Relationships
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    // Scopes
    public function scopeWhatsApp($query)
    {
        return $query->where('channel', 'whatsapp');
    }

    public function scopeTelegram($query)
    {
        return $query->where('channel', 'telegram');
    }

    public function scopeIncoming($query)
    {
        return $query->where('direction', 'incoming');
    }

    public function scopeOutgoing($query)
    {
        return $query->where('direction', 'outgoing');
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('timestamp', 'desc');
    }

    // Accessors
    public function getChannelDisplayAttribute(): string
    {
        return match ($this->channel) {
            'whatsapp' => 'WhatsApp',
            'telegram' => 'Telegram',
            default => ucfirst($this->channel)
        };
    }

    public function getDirectionDisplayAttribute(): string
    {
        return match ($this->direction) {
            'incoming' => 'Incoming',
            'outgoing' => 'Outgoing',
            default => ucfirst($this->direction)
        };
    }

    public function getShortMessageAttribute(): string
    {
        return strlen($this->message) > 50
            ? substr($this->message, 0, 50) . '...'
            : $this->message;
    }

    // Helper methods for chat interface
    public function getCustomerAttribute()
    {
        if ($this->channel === 'whatsapp') {
            return Customer::where('phone_number', $this->sender)
                ->where('account_type', 'whatsapp')
                ->first();
        } else {
            return Customer::where('chat_id', $this->sender)
                ->where('account_type', 'telegram')
                ->first();
        }
    }

    public function getIsFromCustomerAttribute(): bool
    {
        return $this->direction === 'incoming';
    }

    public function getFormattedTimeAttribute(): string
    {
        return $this->timestamp->format('H:i');
    }

    public function getFormattedDateAttribute(): string
    {
        return $this->timestamp->format('d/m/Y');
    }

    // Scope for conversation grouping
    public function scopeForCustomer($query, $customerIdentifier, $channel)
    {
        if ($channel === 'whatsapp') {
            return $query->where('sender', $customerIdentifier)->where('channel', 'whatsapp');
        } else {
            return $query->where('sender', $customerIdentifier)->where('channel', 'telegram');
        }
    }

    public function scopeConversation($query, $customerIdentifier, $channel)
    {
        return $query->forCustomer($customerIdentifier, $channel)
            ->orderBy('timestamp', 'asc');
    }
}
