{"__meta": {"id": "X51a95b12fb4b60bec9b98878acfd7583", "datetime": "2025-05-29 23:37:16", "utime": **********.573687, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.3.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748561835.891591, "end": **********.573724, "duration": 0.6821329593658447, "duration_str": "682ms", "measures": [{"label": "Booting", "start": 1748561835.891591, "relative_start": 0, "end": **********.234025, "relative_end": **********.234025, "duration": 0.3424339294433594, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.234038, "relative_start": 0.*****************, "end": **********.573728, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "340ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\AccountResource\\Pages\\CreateAccount@getFormSelectOptions", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FConcerns%2FInteractsWithForms.php&line=146\" onclick=\"\">vendor/filament/forms/src/Concerns/InteractsWithForms.php:146-155</a>"}, "queries": {"nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0089, "accumulated_duration_str": "8.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks' limit 1", "type": "query", "params": [], "bindings": ["ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.251172, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "omnibis", "explain": null, "start_percent": 0, "width_percent": 11.011}, {"sql": "select * from `users` where `id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4893332, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omnibis", "explain": null, "start_percent": 11.011, "width_percent": 13.371}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (11) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.493656, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omnibis", "explain": null, "start_percent": 24.382, "width_percent": 13.933}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.498215, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "omnibis", "explain": null, "start_percent": 38.315, "width_percent": 11.236}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:11', 'illuminate:cache:flexible:created:filament-excel:exports:11')", "type": "query", "params": [], "bindings": ["filament-excel:exports:11", "illuminate:cache:flexible:created:filament-excel:exports:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 362}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 534}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.500612, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:387", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=387", "ajax": false, "filename": "DatabaseStore.php", "line": "387"}, "connection": "omnibis", "explain": null, "start_percent": 49.551, "width_percent": 7.191}, {"sql": "select `users`.`name`, `users`.`id` from `users` order by `users`.`name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Concerns/SupportsSelectFields.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\forms\\src\\Concerns\\SupportsSelectFields.php", "line": 61}], "start": **********.559684, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "omnibis", "explain": null, "start_percent": 56.742, "width_percent": 9.213}, {"sql": "update `sessions` set `payload` = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiYWs1V1JCT2tyUnpvT2d2S3h3c2FBdUtyQ1FNc280TmNneU5LaTZZTCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM1OiJodHRwOi8vb21uaWJpcy50ZXN0L2FjY291bnRzL2NyZWF0ZSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjExO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkeVQwRTBWdktMY1pQaWZ4bG9CREtCZTNEcXZwemE5V3RTNTJUNjVpRjRkMVNndzk1VUltTDYiO30=', `last_activity` = **********, `user_id` = 11, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiYWs1V1JCT2tyUnpvT2d2S3h3c2FBdUtyQ1FNc280TmNneU5LaTZZTCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM1OiJodHRwOi8vb21uaWJpcy50ZXN0L2FjY291bnRzL2NyZWF0ZSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjExO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkeVQwRTBWdktMY1pQaWZ4bG9CREtCZTNEcXZwemE5V3RTNTJUNjVpRjRkMVNndzk1VUltTDYiO30=", **********, 11, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 176}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.566545, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\laragon\\www\\omnibis\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "omnibis", "explain": null, "start_percent": 65.955, "width_percent": 34.045}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://omnibis.test/accounts/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "11", "password_hash_web": "$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1077 characters\">{&quot;data&quot;:{&quot;record&quot;:null,&quot;data&quot;:[{&quot;user_id&quot;:null,&quot;name&quot;:null,&quot;type&quot;:null,&quot;credentials&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;status&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],&quot;previousUrl&quot;:&quot;http:\\/\\/omnibis.test\\/accounts&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;savedDataHash&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;HOCYIqXzHxRqeBASZFRz&quot;,&quot;name&quot;:&quot;app.filament.resources.account-resource.pages.create-account&quot;,&quot;path&quot;:&quot;accounts\\/create&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;e7a1dd319d8fee47f92bf030f2e215e363494637fba2e8466e474db2cc16b722&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormSelectOptions</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">data.user_id</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1287946928 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1175 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IklUM2tlTG5UL1FqZHdYQUpFQlFCK1E9PSIsInZhbHVlIjoiNDFRVEFBanE2Y3l2WVVEU2hGUC9KTHNMWXQ1WGVkemRUck9IRDRSSHk4ZzVieVhnQWVLMzNld1p3UEdpU0lvQU0vc0Jvc3d4TjZIUEV3MVN3Q3hON1ljUm5yVTVzbGQ1V0NFcHcxdWg3TkR4cHpzOFhhbXE2eDhOaGhXOXRXT1pBWXpiSGN2bUY0b3lyeUhvM29Mam5QNUNERlRjY0Q0TXFEQWo5ajAxU2JBPSIsIm1hYyI6ImVhNjYwYjg3YzFhMTIzODg2M2ZkMjhlOTkwOTMwYWRlZDZjZDYyNDU4MTY5MGYzNzc3MzlmNGVkMmU3ODMwODUiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6Ikh6aGRHRXpLMUltaXRXb3NKa3R5Mmc9PSIsInZhbHVlIjoibW5pcmtuMHJ3emRqZHM1OWYrbEp3N1hlMnlFQ2FoZm0yRmFxeW04MHpQUDdJc1RIc2ZENDJ5Q1UyaVdtU3BWSWxxZ2lMbkVUUUxGYmNpQU5yWlM2VHc3ZnVBMWxZRWpWODEySmtoci9sWkQ4TlRjbFVCUjIwVVpURFBNN0VvdHgiLCJtYWMiOiI4MDBhYzNlMjJjMzgwZTlhZTNmZGFhMzg4Y2IxNGFkOGJlODc1MWU1YTM2YTJkYjkxOTJjZGNhNDQwMDY0OTQ5IiwidGFnIjoiIn0%3D; zapinflow_session=eyJpdiI6IjJWT0dLTW96dHM4MjFjQnpyZHQ5Umc9PSIsInZhbHVlIjoiczJHbzNLSE1XMVF2VjFLd2FuazQ5ZFpweGtoQ3B3NTBIMVJjaERGTGxlcFdZRllTcTh1cUVYdmVyZTBmNU9CZnJJb1dZWm9KeTZ4Z080cWF0WmgxQy9FWm5TNW1CZzV5bXBXMVRYNzhURjlKZTJmcG40eFBlMFRibXAzWTYya3UiLCJtYWMiOiJjNTA0MDkyMzc5OTVmYTQyMTk2ZDhhNWQwYzE2MWU2OWNiZmE5NGI4YWJlNWIwZDA1MGEyYzE4MTA0OTAzODBmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,id;q=0.8,ms;q=0.7,th;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://omnibis.test/accounts/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://omnibis.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1390</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">omnibis.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1287946928\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-72881691 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"74 characters\">11|o5UxToU2uu|$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>zapinflow_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZQ2he8UzX307ppKUFeTYT84Nbbv5WfsPJxlccgks</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72881691\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2136229023 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 29 May 2025 23:37:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InlWSm9ZRG1QNXBKUTVKdW9QRTYwbWc9PSIsInZhbHVlIjoiWjdYQUZINFUwYnI3TEhPL0JTSXZ2R0V4TDQ1QUJhOVhUWmVQbDMreEM2QXp2RCtpaERKRlpWTTZGYUF6d3NJeXpCdmR4QjhYblNCTzM3eU1tZCtJTW5rbWd0Qk01aXV0azluaGM5V0M3QngzeHAwdUhEZTBVdXAxRXFobnFNTWYiLCJtYWMiOiI0YWI2ZmZlNmQxZjI1NWVmMzk4Yjg4MmQ5NzMyNmZjYWZhYjNkNDQ0ZGZiYTczNTAzNjE0MzY4MDY3NmE2YjVlIiwidGFnIjoiIn0%3D; expires=Fri, 30 May 2025 01:37:16 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">zapinflow_session=eyJpdiI6IldrbHJvZUxjaTdnWWNKMXBIZGlKcmc9PSIsInZhbHVlIjoiVUoyNmRURXFGYThsZDJPQ2I2TVlIMjZzRUFEZzZRNEFZTG9aWkZ1ZGl4dkEwUGVDVGZSSmNsMTlIa2JsekpRck93WTl5YnI3WkZzWXkyaEI0QnZXYkVmUFpnZnZIV01oZWg3ZEZ2VTBaRGZYNUlJMFFZamhIcWd0bFZOUHRxQXUiLCJtYWMiOiI2ODUxYWEzYTA4MzMyY2ExZDU1ZWVmZTBlNjNhYWY3NzA2NzNlMWFmNTFlOGJmMjBiNjFmY2YzNjZmMGNmNTVjIiwidGFnIjoiIn0%3D; expires=Fri, 30 May 2025 01:37:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InlWSm9ZRG1QNXBKUTVKdW9QRTYwbWc9PSIsInZhbHVlIjoiWjdYQUZINFUwYnI3TEhPL0JTSXZ2R0V4TDQ1QUJhOVhUWmVQbDMreEM2QXp2RCtpaERKRlpWTTZGYUF6d3NJeXpCdmR4QjhYblNCTzM3eU1tZCtJTW5rbWd0Qk01aXV0azluaGM5V0M3QngzeHAwdUhEZTBVdXAxRXFobnFNTWYiLCJtYWMiOiI0YWI2ZmZlNmQxZjI1NWVmMzk4Yjg4MmQ5NzMyNmZjYWZhYjNkNDQ0ZGZiYTczNTAzNjE0MzY4MDY3NmE2YjVlIiwidGFnIjoiIn0%3D; expires=Fri, 30-May-2025 01:37:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">zapinflow_session=eyJpdiI6IldrbHJvZUxjaTdnWWNKMXBIZGlKcmc9PSIsInZhbHVlIjoiVUoyNmRURXFGYThsZDJPQ2I2TVlIMjZzRUFEZzZRNEFZTG9aWkZ1ZGl4dkEwUGVDVGZSSmNsMTlIa2JsekpRck93WTl5YnI3WkZzWXkyaEI0QnZXYkVmUFpnZnZIV01oZWg3ZEZ2VTBaRGZYNUlJMFFZamhIcWd0bFZOUHRxQXUiLCJtYWMiOiI2ODUxYWEzYTA4MzMyY2ExZDU1ZWVmZTBlNjNhYWY3NzA2NzNlMWFmNTFlOGJmMjBiNjFmY2YzNjZmMGNmNTVjIiwidGFnIjoiIn0%3D; expires=Fri, 30-May-2025 01:37:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2136229023\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak5WRBOkrRzoOgvKxwsaAuKrCQMso4NcgyNKi6YL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://omnibis.test/accounts/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$yT0E0VvKLcZPifxloBDKBe3Dqvpza9WtS52T65iF4d1Sgw95UImL6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}